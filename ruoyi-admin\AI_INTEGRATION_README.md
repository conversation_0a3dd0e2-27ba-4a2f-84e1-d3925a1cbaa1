# AI 智能问答模块集成指南

## 概述

本模块基于 LangChain4j 框架，集成阿里云通义千问(qwen-plus)，实现了流式输出、会话记忆和会话隔离功能。

## 功能特性

- 🤖 **多模型支持**: 支持 qwen-plus、qwen-turbo、qwen-max 等模型
- 💬 **流式输出**: 实时流式响应，提升用户体验
- 🧠 **会话记忆**: 每个会话独立的上下文记忆
- 🔒 **会话隔离**: 不同用户和会话之间完全隔离
- 📊 **数据持久化**: 会话和消息数据库存储
- 🔐 **权限控制**: 集成若依权限系统
- 📈 **监控统计**: 响应时间、Token 使用量统计

## 部署步骤

### 1. 环境准备

确保您已经：

- 获得阿里云 API 密钥
- 配置环境变量 `API-KEY`
- Java 8+ 环境
- MySQL 5.7+ 数据库

### 2. 配置 API 密钥

```bash
# 方式1: 设置环境变量（推荐）
export API-KEY=your-dashscope-api-key

# 方式2: 在application.yml中直接配置
ai:
  qwen:
    api-key: your-dashscope-api-key
```

### 3. 数据库初始化

执行 SQL 脚本创建相关表：

```bash
mysql -u username -p database_name < src/main/resources/sql/ai_chat_tables.sql
```

### 4. 依赖配置

项目已自动添加以下依赖：

```xml
<!-- LangChain4j Spring Boot Starter -->
<dependency>
    <groupId>dev.langchain4j</groupId>
    <artifactId>langchain4j-spring-boot-starter</artifactId>
    <version>0.34.0</version>
</dependency>

<!-- LangChain4j OpenAI Compatible Provider -->
<dependency>
    <groupId>dev.langchain4j</groupId>
    <artifactId>langchain4j-open-ai</artifactId>
    <version>0.34.0</version>
</dependency>
```

### 5. 启动应用

```bash
mvn spring-boot:run
```

## API 接口说明

### 基础 URL

```
http://localhost:8080/ai/chat
```

### 主要接口

#### 1. 创建会话

```http
POST /ai/chat/session
Content-Type: application/json

{
  "title": "新对话",
  "model": "qwen-plus",
  "remark": "测试会话"
}
```

#### 2. 发送消息（同步）

```http
POST /ai/chat/send
Content-Type: application/json

{
  "sessionId": "session-id",
  "message": "你好，请介绍一下自己",
  "model": "qwen-plus",
  "stream": false
}
```

#### 3. 发送消息（流式）

```http
POST /ai/chat/stream
Content-Type: application/json

{
  "sessionId": "session-id",
  "message": "请详细解释一下人工智能",
  "model": "qwen-plus",
  "stream": true
}
```

#### 4. 获取会话列表

```http
GET /ai/chat/sessions
```

#### 5. 获取聊天历史

```http
GET /ai/chat/history?sessionId=session-id
```

#### 6. 删除会话

```http
DELETE /ai/chat/session/{sessionId}
```

#### 7. 清空会话消息

```http
DELETE /ai/chat/clear/{sessionId}
```

## 配置说明

### application.yml 配置

```yaml
ai:
  qwen:
    # API密钥（从环境变量获取）
    api-key: ${API_KEY:your-api-key-here}
    # 基础URL
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    # 默认模型
    model: qwen-plus
    # 超时时间（秒）
    timeout: 60
    # 最大Token数
    max-tokens: 2000
    # 温度参数
    temperature: 0.7
```

### 权限配置

系统自动创建以下权限：

- `ai:chat:view` - 查看智能问答页面
- `ai:chat:send` - 发送消息
- `ai:chat:history` - 查看历史记录
- `ai:chat:session` - 管理会话
- `ai:chat:export` - 导出记录

## 测试验证

### 1. 测试 AI 连接

```http
GET /ai/test/connection
```

### 2. 测试简单对话

```http
POST /ai/test/chat
Content-Type: application/x-www-form-urlencoded

message=你好，请介绍一下自己
```

### 3. 前端集成

前端页面路径：`http://localhost:8080/ai/chat`

## 核心特性详解

### 1. 流式输出

使用 Server-Sent Events (SSE) 实现实时流式响应：

```javascript
const eventSource = new EventSource("/ai/chat/stream");
eventSource.onmessage = function (event) {
  const data = JSON.parse(event.data);
  console.log(data.content);
};
```

### 2. 会话记忆

每个会话维护独立的上下文记忆，支持：

- 最多 20 轮对话记忆
- 自动上下文管理
- 会话隔离保证

### 3. 会话隔离

- 用户级别隔离：不同用户无法访问他人会话
- 会话级别隔离：不同会话之间上下文独立
- 权限验证：每次请求都验证用户权限

## 监控和日志

### 1. 日志配置

```yaml
logging:
  level:
    com.ruoyi.web.controller.ai: debug
    dev.langchain4j: info
```

### 2. 监控指标

- 响应时间统计
- Token 使用量统计
- 错误率监控
- 会话活跃度统计

## 故障排除

### 1. 常见问题

- **API 密钥错误**: 检查环境变量 API_KEY 是否正确设置
- **网络连接问题**: 确保服务器可以访问 dashscope.aliyuncs.com
- **权限不足**: 检查用户是否具有相应的 AI 模块权限
- **会话不存在**: 确保会话 ID 正确且用户有访问权限

### 2. 调试方法

```bash
# 查看详细日志
tail -f logs/ruoyi.log | grep -i "ai\|langchain"

# 测试API连接
curl -X GET "http://localhost:8080/ai/test/connection"
```

## 扩展开发

### 1. 添加新的 AI 模型

在 AiConfig.java 中添加新的模型配置

### 2. 自定义系统提示词

修改 AiConfig.AiAssistant 接口中的@SystemMessage 注解

### 3. 集成其他 AI 服务

实现新的 ChatLanguageModel 或 StreamingChatLanguageModel

## 安全注意事项

1. 妥善保管 API 密钥，不要提交到代码仓库
2. 定期轮换 API 密钥
3. 监控 API 使用量，避免超出配额
4. 对用户输入进行适当的过滤和验证
5. 定期备份会话数据

## 更新日志

- v1.0.0: 初始版本，支持基础聊天功能
- 集成 LangChain4j 框架
- 支持阿里云通义千问
- 实现流式输出和会话记忆
