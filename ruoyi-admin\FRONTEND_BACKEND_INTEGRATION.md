# AI 模块前后端联调指南

## 🎯 联调目标

确保前端 Vue3 应用能够正确调用后端 Spring Boot 的 AI 接口，实现完整的 AI 智能问答功能。

## 🔧 已完成的修改

### 后端修改

1. **修复编译错误** - 解决了`AjaxResult.success()`方法调用问题
2. **API 接口完善** - 所有 AI 相关接口已实现
3. **响应格式统一** - 使用若依框架标准响应格式

### 前端修改

1. **禁用模拟数据** - 改为调用真实后端 API
2. **响应格式适配** - 适配若依框架的响应格式
3. **错误处理增强** - 添加详细的错误信息显示
4. **测试页面** - 创建专门的联调测试页面

## 🚀 联调步骤

### 1. 环境准备

```bash
# 设置API密钥（必须）
export API-KEY=your-dashscope-api-key

# 验证环境变量
echo $API-KEY
```

### 2. 数据库准备

```sql
-- 执行AI模块数据库脚本
mysql -u username -p database_name < ruoyi-admin/src/main/resources/sql/ai_chat_tables.sql
```

### 3. 启动后端服务

```bash
cd ruoyi-admin
mvn clean compile
mvn spring-boot:run
```

**验证后端启动成功**：

- 控制台显示 "Started RuoYiApplication"
- 访问：http://localhost:8080/ai/test/connection

### 4. 启动前端服务

```bash
cd ruoyi-ui/RuoYi-Vue3
npm install  # 如果是首次运行
npm run dev
```

**验证前端启动成功**：

- 控制台显示 "Local: http://localhost:80"
- 访问：http://localhost:80

### 5. 登录系统

使用默认账号登录：

- 用户名：`admin`
- 密码：`admin123`

### 6. 访问测试页面

**方式 1：直接访问测试页面**
http://localhost:80/ai/test

**方式 2：访问正式聊天页面**
http://localhost:80/ai/chat（需要菜单权限）

## 🧪 测试流程

### 测试页面功能

1. **测试 AI 连接** - 验证后端 AI 服务是否正常
2. **创建会话测试** - 测试会话创建功能
3. **发送消息测试** - 测试 AI 对话功能
4. **获取会话列表** - 测试会话管理
5. **获取聊天历史** - 测试历史记录

### 预期结果

每个测试都应该返回 JSON 格式的响应：

```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    // 具体数据
  }
}
```

## 🔍 调试方法

### 1. 浏览器开发者工具

- 打开 F12 开发者工具
- 查看 Network 标签页
- 观察 API 请求和响应

### 2. 后端日志

```bash
# 查看后端日志
tail -f logs/ruoyi.log | grep -i "ai\|error"
```

### 3. 前端控制台

查看浏览器控制台的错误信息和日志输出

## ❗ 常见问题及解决方案

### 1. 跨域问题

**现象**: 前端请求被 CORS 策略阻止

**解决**: 检查`ruoyi-ui/RuoYi-Vue3/vue.config.js`中的代理配置：

```javascript
devServer: {
  proxy: {
    '/dev-api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: {
        '^/dev-api': ''
      }
    }
  }
}
```

### 2. 认证问题

**现象**: 返回 401 未授权错误

**解决**:

1. 确保已登录系统
2. 检查 token 是否有效
3. 确认用户具有 AI 模块权限

### 3. API 路径问题

**现象**: 404 Not Found 错误

**解决**:

1. 检查后端接口路径是否正确
2. 确认后端服务已启动
3. 验证路由配置

### 4. 数据格式问题

**现象**: 前端无法正确解析后端响应

**解决**:

1. 检查后端响应格式是否符合若依标准
2. 确认前端数据解析逻辑
3. 查看具体的响应数据结构

### 5. AI 服务问题

**现象**: AI 连接测试失败

**解决**:

1. 检查 API_KEY 环境变量是否设置
2. 确认网络可以访问阿里云 API
3. 验证 API 密钥是否有效

## 📊 接口对应关系

| 前端 API            | 后端接口                       | 功能         |
| ------------------- | ------------------------------ | ------------ |
| `createChatSession` | `POST /ai/chat/session`        | 创建会话     |
| `sendMessage`       | `POST /ai/chat/send`           | 发送消息     |
| `sendMessageStream` | `POST /ai/chat/stream`         | 流式聊天     |
| `getChatSessions`   | `GET /ai/chat/sessions`        | 获取会话列表 |
| `getChatHistory`    | `GET /ai/chat/history`         | 获取聊天历史 |
| `deleteChatSession` | `DELETE /ai/chat/session/{id}` | 删除会话     |
| `clearChatHistory`  | `DELETE /ai/chat/clear/{id}`   | 清空历史     |

## 🎉 联调成功标志

1. ✅ 测试页面所有功能正常
2. ✅ AI 能够正常回复消息
3. ✅ 会话管理功能正常
4. ✅ 聊天历史正确显示
5. ✅ 错误处理机制有效

## 📝 下一步

联调成功后可以：

1. **完善 UI 界面** - 优化聊天界面的用户体验
2. **添加流式聊天** - 实现实时流式响应
3. **增强功能** - 添加文件上传、图片识别等
4. **性能优化** - 优化响应速度和用户体验
5. **部署上线** - 部署到生产环境

## 🆘 获取帮助

如果遇到问题：

1. 查看本文档的常见问题部分
2. 检查浏览器控制台和网络请求
3. 查看后端日志文件
4. 使用测试页面进行逐步调试
