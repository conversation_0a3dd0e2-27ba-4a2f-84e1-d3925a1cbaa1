-- 知识库策略管理相关表结构

-- 策略模板表
DROP TABLE IF EXISTS `knowledge_strategy_template`;
CREATE TABLE `knowledge_strategy_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '策略模板ID',
  `name` varchar(100) NOT NULL COMMENT '策略模板名称',
  `description` varchar(500) DEFAULT NULL COMMENT '策略模板描述',
  `strategy_type` varchar(50) NOT NULL COMMENT '策略类型(INITIALIZATION,SEGMENTATION,TAGGING,SUMMARIZATION,ASSOCIATION)',
  `config_json` text COMMENT '策略配置JSON',
  `is_default` char(1) DEFAULT '0' COMMENT '是否默认策略(0否 1是)',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用(0禁用 1启用)',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(64) DEFAULT NULL COMMENT '创建者姓名',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_type` (`strategy_type`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='知识库策略模板表';

-- 知识库策略配置表
DROP TABLE IF EXISTS `knowledge_base_strategy_config`;
CREATE TABLE `knowledge_base_strategy_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `knowledge_base_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `strategy_template_id` bigint(20) NOT NULL COMMENT '策略模板ID',
  `strategy_type` varchar(50) NOT NULL COMMENT '策略类型',
  `config_json` text COMMENT '个性化配置JSON',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用(0禁用 1启用)',
  `execution_order` int(11) DEFAULT 0 COMMENT '执行顺序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_strategy_template_id` (`strategy_template_id`),
  KEY `idx_strategy_type` (`strategy_type`),
  CONSTRAINT `fk_kb_strategy_knowledge_base` FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_base` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_kb_strategy_template` FOREIGN KEY (`strategy_template_id`) REFERENCES `knowledge_strategy_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='知识库策略配置表';

-- 策略执行日志表
DROP TABLE IF EXISTS `knowledge_strategy_execution_log`;
CREATE TABLE `knowledge_strategy_execution_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `knowledge_base_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `strategy_config_id` bigint(20) NOT NULL COMMENT '策略配置ID',
  `strategy_type` varchar(50) NOT NULL COMMENT '策略类型',
  `execution_status` varchar(20) NOT NULL COMMENT '执行状态(PENDING,RUNNING,SUCCESS,FAILED)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration_ms` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `input_data` text COMMENT '输入数据',
  `output_data` text COMMENT '输出数据',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_strategy_config_id` (`strategy_config_id`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='策略执行日志表';

-- 插入默认策略模板数据
INSERT INTO `knowledge_strategy_template` (`name`, `description`, `strategy_type`, `config_json`, `is_default`, `is_enabled`, `sort_order`, `creator_name`, `create_by`, `create_time`) VALUES
('默认初始化策略', '知识库的默认初始化策略，包含基础的创建和配置流程', 'INITIALIZATION', '{"enableVectorization": true, "enableMetadata": true, "batchSize": 100}', '1', '1', 1, 'system', 'system', NOW()),
('递归文档分割策略', '使用递归方式分割文档，适合大多数文档类型', 'SEGMENTATION', '{"maxChunkSize": 500, "chunkOverlap": 50, "separators": ["\\n\\n", "\\n", " ", ""]}', '1', '1', 1, 'system', 'system', NOW()),
('智能标签生成策略', '基于AI的智能标签生成策略', 'TAGGING', '{"maxTags": 5, "useAI": true, "keywordExtraction": true}', '1', '1', 1, 'system', 'system', NOW()),
('AI摘要生成策略', '使用AI生成知识段落的摘要', 'SUMMARIZATION', '{"maxSummaryLength": 200, "useAI": true, "language": "zh"}', '1', '1', 1, 'system', 'system', NOW()),
('语义相似度关联策略', '基于语义相似度建立知识关联', 'ASSOCIATION', '{"similarityThreshold": 0.7, "maxAssociations": 10, "useEmbedding": true}', '1', '1', 1, 'system', 'system', NOW());
