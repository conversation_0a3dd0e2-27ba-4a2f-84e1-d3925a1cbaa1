# AI 会话功能后端实现指南

## 概述

本文档描述了基于 LangChain4j 和阿里云通义千问(qwen-plus)的 AI 会话功能后端实现，包括会话记忆、流式调用和 AiServices 工具类的使用。

## 功能特性

- 🤖 **AI 对话**: 基于 LangChain4j 框架集成阿里云通义千问
- 💬 **会话记忆**: 每个会话独立的上下文记忆，支持最多 20 轮对话
- 🔄 **流式输出**: 支持 Server-Sent Events (SSE)实时流式响应
- 🔒 **会话隔离**: 不同用户和会话之间完全隔离
- 📊 **数据持久化**: 会话和消息数据库存储
- 🔐 **权限控制**: 集成若依权限系统

## 项目结构

```
ruoyi-system/src/main/java/com/ruoyi/ai/
├── config/
│   └── AiConfig.java                    # AI配置类，LangChain4j配置
├── domain/
│   ├── AiChatSession.java              # 会话实体类
│   ├── AiChatMessage.java              # 消息实体类
│   └── dto/
│       ├── AiChatRequest.java          # 聊天请求DTO
│       ├── AiChatResponse.java         # 聊天响应DTO
│       └── AiSessionRequest.java       # 会话创建请求DTO
├── mapper/
│   ├── AiChatSessionMapper.java        # 会话Mapper接口
│   └── AiChatMessageMapper.java        # 消息Mapper接口
└── service/
    ├── IAiChatService.java             # AI聊天综合服务接口
    ├── IAiChatSessionService.java      # 会话服务接口
    ├── IAiChatMessageService.java      # 消息服务接口
    └── impl/
        ├── AiChatServiceImpl.java      # AI聊天综合服务实现
        ├── AiChatSessionServiceImpl.java # 会话服务实现
        └── AiChatMessageServiceImpl.java # 消息服务实现

ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/
├── AiChatController.java               # AI聊天控制器
└── AiTestController.java               # AI测试控制器

ruoyi-system/src/main/resources/mapper/ai/
├── AiChatSessionMapper.xml             # 会话Mapper XML
└── AiChatMessageMapper.xml             # 消息Mapper XML
```

## 核心组件说明

### 1. AiConfig.java - AI 配置类

- 配置 LangChain4j 的 ChatLanguageModel 和 StreamingChatLanguageModel
- 管理会话记忆(ChatMemory)，支持会话隔离
- 提供 AiAssistant 接口，封装 AI 对话逻辑
- 支持同步和流式两种调用方式

### 2. AiChatService - 综合服务层

- 整合会话管理、消息处理、AI 调用等功能
- 实现会话权限检查和用户隔离
- 处理同步和流式 AI 对话
- 管理会话记忆的生命周期

### 3. AiChatController - REST API 控制器

- 提供前端调用的 REST API 接口
- 支持会话创建、消息发送、历史查询等功能
- 实现流式响应的 SSE 接口
- 集成若依权限控制

## API 接口说明

### 主要接口

| 接口           | 方法   | 路径                    | 功能                   | 权限              |
| -------------- | ------ | ----------------------- | ---------------------- | ----------------- |
| 创建会话       | POST   | `/ai/chat/session`      | 创建新的聊天会话       | `ai:chat:session` |
| 获取会话列表   | GET    | `/ai/chat/sessions`     | 获取用户的会话列表     | `ai:chat:view`    |
| 获取聊天历史   | GET    | `/ai/chat/history`      | 获取指定会话的消息历史 | `ai:chat:history` |
| 发送消息(同步) | POST   | `/ai/chat/send`         | 发送消息并获取 AI 回复 | `ai:chat:send`    |
| 发送消息(流式) | POST   | `/ai/chat/stream`       | 流式发送消息           | `ai:chat:send`    |
| 删除会话       | DELETE | `/ai/chat/session/{id}` | 删除指定会话           | `ai:chat:session` |
| 清空历史       | DELETE | `/ai/chat/clear/{id}`   | 清空会话消息           | `ai:chat:session` |
| 获取模型列表   | GET    | `/ai/chat/models`       | 获取可用的 AI 模型     | `ai:chat:view`    |

### 测试接口

| 接口         | 方法   | 路径                          | 功能             |
| ------------ | ------ | ----------------------------- | ---------------- |
| 连接测试     | GET    | `/ai/test/connection`         | 测试 AI 服务连接 |
| 简单聊天     | POST   | `/ai/test/chat`               | 测试基础对话功能 |
| 会话记忆测试 | POST   | `/ai/test/memory`             | 测试会话记忆功能 |
| 清除记忆     | DELETE | `/ai/test/memory/{sessionId}` | 清除指定会话记忆 |
| 系统信息     | GET    | `/ai/test/system`             | 获取系统配置信息 |
| 健康检查     | GET    | `/ai/test/health`             | 检查服务健康状态 |

## 配置说明

### application.yml 配置

```yaml
# LangChain4j配置
langchain4j:
  open-ai:
    chat-model:
      base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
      api-key: ${API-KEY}
      model-name: qwen-plus
      timeout: 60s
      max-tokens: 2000
      temperature: 0.7
      log-request: true
      log-response: true
```

### 环境变量

```bash
# 设置阿里云API密钥
export API_KEY=your-dashscope-api-key
```

## 部署步骤

### 1. 数据库初始化

```bash
# 执行SQL脚本创建表结构
mysql -u username -p database_name < ruoyi-admin/src/main/resources/sql/ai_chat_tables.sql
```

### 2. 配置 API 密钥

```bash
# 设置环境变量
export API_KEY=your-dashscope-api-key

# 验证设置
echo $API_KEY
```

### 3. 编译和启动

```bash
cd ruoyi-admin
mvn clean compile
mvn spring-boot:run
```

### 4. 测试验证

```bash
# 测试AI连接
curl -X GET "http://localhost:8080/ai/test/connection"

# 测试简单对话
curl -X POST "http://localhost:8080/ai/test/chat" -d "message=你好"

# 测试健康检查
curl -X GET "http://localhost:8080/ai/test/health"
```

## 使用示例

### 创建会话

```bash
curl -X POST "http://localhost:8080/ai/chat/session" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试对话",
    "model": "qwen-plus",
    "remark": "这是一个测试会话"
  }'
```

### 发送消息

```bash
curl -X POST "http://localhost:8080/ai/chat/send" \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "your-session-id",
    "message": "你好，请介绍一下自己",
    "model": "qwen-plus"
  }'
```

## 技术特点

### 1. 会话记忆管理

- 使用 LangChain4j 的 MessageWindowChatMemory
- 每个会话独立的记忆空间
- 支持最多 20 轮对话上下文
- 自动清理和管理记忆生命周期

### 2. 流式响应处理

- 基于 Server-Sent Events (SSE)
- 实时推送 AI 生成的内容
- 支持错误处理和连接管理
- 异步处理提升性能

### 3. 权限和安全

- 集成若依权限系统
- 用户级别的会话隔离
- 输入验证和错误处理
- API 密钥安全管理

## 故障排除

### 常见问题

1. **API 密钥错误**: 检查环境变量 API_KEY 是否正确设置
2. **网络连接问题**: 确保服务器可以访问 dashscope.aliyuncs.com
3. **权限不足**: 检查用户是否具有相应的 AI 模块权限
4. **会话不存在**: 确保会话 ID 正确且用户有访问权限

### 调试方法

```bash
# 查看详细日志
tail -f logs/ruoyi.log | grep -i "ai\|langchain"

# 测试API连接
curl -X GET "http://localhost:8080/ai/test/connection"

# 检查系统状态
curl -X GET "http://localhost:8080/ai/test/system"
```

## 扩展开发

### 1. 添加新的 AI 模型

在 AiConfig.java 中修改模型配置，在 getAvailableModels()方法中添加新模型。

### 2. 自定义系统提示词

修改 AiConfig.AiAssistant 接口中的@SystemMessage 注解内容。

### 3. 集成其他 AI 服务

实现新的 ChatLanguageModel 或 StreamingChatLanguageModel。

## 更新日志

- v1.0.0: 初始版本，支持基础聊天功能
- 集成 LangChain4j 框架
- 支持阿里云通义千问
- 实现流式输出和会话记忆
- 完整的权限控制和数据持久化
