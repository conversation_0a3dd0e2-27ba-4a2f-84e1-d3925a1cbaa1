# AI聊天超时配置优化指南

## 问题描述

用户反馈AI聊天接口经常报错，但刷新后仍能得到回答，这表明问题出现在超时配置过短，而不是AI服务本身的问题。

## 解决方案

### 1. 前端超时配置优化

#### 1.1 全局axios超时调整
**文件**: `ruoyi-ui/RuoYi-Vue3/src/utils/request.js`
- 将默认超时从 `10000ms (10秒)` 增加到 `120000ms (2分钟)`

#### 1.2 AI专用请求实例
**文件**: `ruoyi-ui/RuoYi-Vue3/src/api/ai/chat.js`
- 创建专门的AI请求axios实例
- 超时时间设置为 `180000ms (3分钟)`
- 专门的错误处理，提供更友好的超时提示

```javascript
// 创建专门用于AI请求的axios实例
const aiRequest = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 180000, // 3分钟超时
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})
```

### 2. 后端超时配置优化

#### 2.1 LangChain4j配置
**文件**: `ruoyi-admin/src/main/resources/application.yml`
- 将LangChain4j超时从 `60s` 增加到 `150s`
- 将AI模块配置超时从 `60s` 增加到 `150s`

```yaml
# LangChain4j配置
langchain4j:
  open-ai:
    chat-model:
      timeout: 150s  # 从60s增加到150s

# AI模块配置
ai:
  qwen:
    timeout: 150  # 从60增加到150秒
```

#### 2.2 AiConfig Java配置
**文件**: `ruoyi-system/src/main/java/com/ruoyi/ai/config/AiConfig.java`
- ChatLanguageModel超时: `Duration.ofSeconds(150)`
- StreamingChatLanguageModel超时: `Duration.ofSeconds(150)`

#### 2.3 Tomcat连接超时
**文件**: `ruoyi-admin/src/main/resources/application.yml`
- 添加Tomcat连接超时配置: `connection-timeout: 180000` (3分钟)

### 3. 用户体验优化

#### 3.1 加载状态提示
**文件**: `ruoyi-ui/RuoYi-Vue3/src/views/ai/chat/index.vue`
- 发送消息时显示"AI正在思考中，请稍候..."
- 收到回复或出错时移除思考提示

#### 3.2 错误信息优化
- 区分不同类型的错误（超时、网络、服务器错误）
- 提供更友好的错误提示信息

```javascript
if (error.message && error.message.includes('timeout')) {
  errorMessage = 'AI响应超时，请稍后重试'
} else if (error.message && error.message.includes('网络')) {
  errorMessage = '网络连接异常，请检查网络后重试'
} else if (error.response && error.response.status === 500) {
  errorMessage = 'AI服务暂时不可用，请稍后重试'
}
```

## 超时配置层级

```
前端请求超时: 180秒 (3分钟)
    ↓
Tomcat连接超时: 180秒 (3分钟)
    ↓
LangChain4j超时: 150秒 (2.5分钟)
    ↓
阿里云API超时: 由阿里云控制
```

## 测试验证

### 1. 创建超时测试页面
**文件**: `ruoyi-ui/RuoYi-Vue3/src/views/ai/timeout-test/index.vue`
- 专门用于测试AI响应时间的页面
- 可以输入复杂问题测试长时间响应
- 显示详细的时间统计和错误信息

### 2. 测试步骤
1. 访问超时测试页面
2. 输入复杂的问题（如要求详细解释某个主题）
3. 观察响应时间和成功率
4. 验证错误提示是否友好

## 配置建议

### 生产环境建议
- 前端超时: 120-180秒
- 后端超时: 90-150秒
- 根据实际AI模型响应时间调整

### 开发环境建议
- 可以设置更长的超时时间便于调试
- 启用详细的日志记录

## 监控和维护

### 1. 日志监控
- 监控AI请求的响应时间
- 记录超时和失败的请求
- 分析用户使用模式

### 2. 性能优化
- 定期检查AI模型响应时间
- 根据使用情况调整超时配置
- 考虑实现请求队列和限流

## 故障排除

### 常见问题
1. **仍然超时**: 检查网络连接和API密钥
2. **响应慢**: 考虑使用更快的AI模型
3. **频繁失败**: 检查阿里云API配额和限制

### 调试方法
```bash
# 查看详细日志
tail -f logs/ruoyi.log | grep -i "timeout\|ai"

# 测试网络连接
curl -X GET "http://localhost:8080/ai/test/connection"

# 使用超时测试页面进行详细测试
```

## 更新记录

- 2024-07-10: 初始版本，全面优化超时配置
- 增加前端AI专用请求实例
- 优化用户体验和错误提示
- 创建超时测试工具
