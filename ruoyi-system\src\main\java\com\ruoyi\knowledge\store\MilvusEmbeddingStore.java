package com.ruoyi.knowledge.store;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.collection.LoadCollectionParam;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.grpc.SearchResults;
import io.milvus.grpc.MutationResult;
import io.milvus.response.SearchResultsWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Milvus向量存储实现
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Component
public class MilvusEmbeddingStore implements EmbeddingStore<TextSegment> {

    private static final Logger logger = LoggerFactory.getLogger(MilvusEmbeddingStore.class);

    @Autowired
    private MilvusServiceClient milvusClient;

    @Autowired
    private com.ruoyi.knowledge.config.MilvusConfig milvusConfig;

    private String collectionName;

    @PostConstruct
    public void init() {
        this.collectionName = milvusConfig.getCollectionName();
        
        // 加载集合到内存
        try {
            LoadCollectionParam loadParam = LoadCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();
            milvusClient.loadCollection(loadParam);
            logger.info("Milvus集合 {} 加载到内存成功", collectionName);
        } catch (Exception e) {
            logger.error("加载Milvus集合失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public String add(Embedding embedding) {
        return add(embedding, null);
    }

    @Override
    public void add(String id, Embedding embedding) {
        add(id, embedding, null);
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        String id = UUID.randomUUID().toString();
        add(id, embedding, textSegment);
        return id;
    }

    @Override
    public void add(String id, Embedding embedding, TextSegment textSegment) {
        try {
            List<String> ids = Arrays.asList(id);
            List<List<Float>> vectors = Arrays.asList(embedding.vectorAsList());
            
            // 准备插入数据
            List<String> texts = Arrays.asList(textSegment != null ? textSegment.text() : "");
            List<String> knowledgeBaseIds = Arrays.asList(
                textSegment != null && textSegment.metadata() != null ? 
                textSegment.metadata().getString("knowledgeBaseId", "") : ""
            );
            List<String> documentIds = Arrays.asList(
                textSegment != null && textSegment.metadata() != null ? 
                textSegment.metadata().getString("documentId", "") : ""
            );
            List<String> documentNames = Arrays.asList(
                textSegment != null && textSegment.metadata() != null ? 
                textSegment.metadata().getString("documentName", "") : ""
            );
            List<String> documentTypes = Arrays.asList(
                textSegment != null && textSegment.metadata() != null ? 
                textSegment.metadata().getString("documentType", "") : ""
            );

            List<InsertParam.Field> fields = Arrays.asList(
                new InsertParam.Field("id", ids),
                new InsertParam.Field("vector", vectors),
                new InsertParam.Field("text", texts),
                new InsertParam.Field("knowledge_base_id", knowledgeBaseIds),
                new InsertParam.Field("document_id", documentIds),
                new InsertParam.Field("document_name", documentNames),
                new InsertParam.Field("document_type", documentTypes)
            );

            InsertParam insertParam = InsertParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withFields(fields)
                    .build();

            milvusClient.insert(insertParam);
            logger.debug("向Milvus插入向量成功，ID: {}", id);
            
        } catch (Exception e) {
            logger.error("向Milvus插入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入向量失败", e);
        }
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        return addAll(embeddings, null);
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> textSegments) {
        List<String> ids = new ArrayList<>();
        
        try {
            List<List<Float>> vectors = new ArrayList<>();
            List<String> texts = new ArrayList<>();
            List<String> knowledgeBaseIds = new ArrayList<>();
            List<String> documentIds = new ArrayList<>();
            List<String> documentNames = new ArrayList<>();
            List<String> documentTypes = new ArrayList<>();

            for (int i = 0; i < embeddings.size(); i++) {
                String id = UUID.randomUUID().toString();
                ids.add(id);
                
                vectors.add(embeddings.get(i).vectorAsList());
                
                TextSegment textSegment = textSegments != null && i < textSegments.size() ? textSegments.get(i) : null;
                texts.add(textSegment != null ? textSegment.text() : "");
                
                if (textSegment != null && textSegment.metadata() != null) {
                    knowledgeBaseIds.add(textSegment.metadata().getString("knowledgeBaseId", ""));
                    documentIds.add(textSegment.metadata().getString("documentId", ""));
                    documentNames.add(textSegment.metadata().getString("documentName", ""));
                    documentTypes.add(textSegment.metadata().getString("documentType", ""));
                } else {
                    knowledgeBaseIds.add("");
                    documentIds.add("");
                    documentNames.add("");
                    documentTypes.add("");
                }
            }

            List<InsertParam.Field> fields = Arrays.asList(
                new InsertParam.Field("id", ids),
                new InsertParam.Field("vector", vectors),
                new InsertParam.Field("text", texts),
                new InsertParam.Field("knowledge_base_id", knowledgeBaseIds),
                new InsertParam.Field("document_id", documentIds),
                new InsertParam.Field("document_name", documentNames),
                new InsertParam.Field("document_type", documentTypes)
            );

            InsertParam insertParam = InsertParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withFields(fields)
                    .build();

            milvusClient.insert(insertParam);
            logger.info("批量向Milvus插入 {} 个向量成功", embeddings.size());
            
        } catch (Exception e) {
            logger.error("批量插入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量插入向量失败", e);
        }
        
        return ids;
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest request) {
        try {
            List<String> searchOutputFields = Arrays.asList("id", "text", "knowledge_base_id", "document_id", "document_name", "document_type");
            
            SearchParam searchParam = SearchParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withMetricType(io.milvus.grpc.MetricType.COSINE)
                    .withOutFields(searchOutputFields)
                    .withTopK(request.maxResults())
                    .withVectors(Arrays.asList(request.queryEmbedding().vectorAsList()))
                    .withVectorFieldName("vector")
                    .withParams("{\"nprobe\":10}")
                    .build();

            SearchResults searchResults = milvusClient.search(searchParam).getData().getResults();
            SearchResultsWrapper wrapper = new SearchResultsWrapper(searchResults);
            
            List<EmbeddingMatch<TextSegment>> matches = new ArrayList<>();
            
            for (int i = 0; i < wrapper.getRowCount(); i++) {
                float score = wrapper.getIDScore(0).get(i).getScore();
                
                // 应用最小分数过滤
                if (score < request.minScore()) {
                    continue;
                }
                
                String id = wrapper.getIDScore(0).get(i).getStrID();
                String text = (String) wrapper.getFieldData("text", 0).get(i);
                String knowledgeBaseId = (String) wrapper.getFieldData("knowledge_base_id", 0).get(i);
                String documentId = (String) wrapper.getFieldData("document_id", 0).get(i);
                String documentName = (String) wrapper.getFieldData("document_name", 0).get(i);
                String documentType = (String) wrapper.getFieldData("document_type", 0).get(i);
                
                // 构建TextSegment
                TextSegment textSegment = TextSegment.from(text);
                if (knowledgeBaseId != null && !knowledgeBaseId.isEmpty()) {
                    textSegment.metadata().put("knowledgeBaseId", knowledgeBaseId);
                }
                if (documentId != null && !documentId.isEmpty()) {
                    textSegment.metadata().put("documentId", documentId);
                }
                if (documentName != null && !documentName.isEmpty()) {
                    textSegment.metadata().put("documentName", documentName);
                }
                if (documentType != null && !documentType.isEmpty()) {
                    textSegment.metadata().put("documentType", documentType);
                }
                
                matches.add(new EmbeddingMatch<>(score, id, null, textSegment));
            }
            
            logger.debug("Milvus搜索返回 {} 个结果", matches.size());
            return new EmbeddingSearchResult<>(matches);
            
        } catch (Exception e) {
            logger.error("Milvus搜索失败: {}", e.getMessage(), e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    /**
     * 根据知识库ID删除向量
     */
    public void removeByKnowledgeBaseId(String knowledgeBaseId) {
        try {
            String expr = String.format("knowledge_base_id == \"%s\"", knowledgeBaseId);
            
            DeleteParam deleteParam = DeleteParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withExpr(expr)
                    .build();
            
            milvusClient.delete(deleteParam);
            logger.info("删除知识库 {} 的所有向量成功", knowledgeBaseId);
            
        } catch (Exception e) {
            logger.error("删除知识库向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除向量失败", e);
        }
    }

    /**
     * 根据文档ID删除向量
     */
    public void removeByDocumentId(String documentId) {
        try {
            String expr = String.format("document_id == \"%s\"", documentId);
            
            DeleteParam deleteParam = DeleteParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withExpr(expr)
                    .build();
            
            milvusClient.delete(deleteParam);
            logger.info("删除文档 {} 的所有向量成功", documentId);
            
        } catch (Exception e) {
            logger.error("删除文档向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除向量失败", e);
        }
    }
}
