# 策略配置功能问题排查指南

## 问题描述
用户点击"配置知识库策略"按钮后，策略选择弹窗显示"使用默认策略配置"，选择策略后前端页面的策略数量不更新。

## 排查步骤

### 1. 检查浏览器控制台
打开浏览器开发者工具（F12），查看Console标签页：

1. **查看API调用日志**
   - 应该看到类似以下的日志：
   ```
   打开策略配置对话框
   开始加载策略类型和模板
   开始调用getStrategyTypes API
   getStrategyTypes响应: {code: 200, data: [...]}
   ```

2. **查看网络请求**
   - 切换到Network标签页
   - 点击"配置知识库策略"按钮
   - 查看是否有以下请求：
     - `GET /knowledge/strategy/types`
     - `GET /knowledge/strategy/template/type/INITIALIZATION`
     - `GET /knowledge/strategy/template/type/SEGMENTATION`

### 2. 检查后端服务状态

1. **确认后端服务已启动**
   ```bash
   # 检查服务是否运行
   ps aux | grep java
   
   # 检查端口是否监听
   netstat -tlnp | grep 8080
   ```

2. **检查后端日志**
   ```bash
   # 查看应用日志
   tail -f logs/sys-info.log
   
   # 查看是否有策略相关的错误
   grep -i "strategy" logs/sys-error.log
   ```

### 3. 检查数据库数据

1. **检查策略模板表是否有数据**
   ```sql
   SELECT * FROM knowledge_strategy_template;
   ```

2. **如果没有数据，执行插入脚本**
   ```bash
   mysql -u root -p your_database < sql/insert_test_strategy_data.sql
   ```

### 4. 测试API接口

1. **使用测试页面**
   - 打开 `test_strategy_api.html`
   - 点击"测试获取策略类型"按钮
   - 查看是否返回正确数据

2. **使用curl命令测试**
   ```bash
   # 测试策略类型接口
   curl -X GET "http://localhost:8080/knowledge/strategy/types"
   
   # 测试策略模板接口
   curl -X GET "http://localhost:8080/knowledge/strategy/template/type/INITIALIZATION"
   ```

### 5. 检查权限配置

1. **确认用户有相关权限**
   - 登录系统管理
   - 检查当前用户角色是否有 `knowledge:strategy:query` 权限

2. **临时移除权限检查**（仅用于测试）
   - 在 `KnowledgeStrategyController.java` 中注释掉 `@PreAuthorize` 注解

### 6. 前端调试

1. **检查Vue组件状态**
   在浏览器控制台中执行：
   ```javascript
   // 查看策略相关数据
   console.log('策略类型:', window.strategyTypes);
   console.log('策略模板:', window.strategyTemplatesByType);
   console.log('选中策略:', window.selectedStrategies);
   ```

2. **手动触发数据加载**
   ```javascript
   // 在控制台中手动调用
   loadStrategyTypes();
   ```

## 常见问题及解决方案

### 问题1: API返回404错误
**原因**: 后端Controller路径配置错误或服务未启动
**解决**: 
- 检查 `@RequestMapping("/knowledge/strategy")` 路径
- 确认后端服务正常启动

### 问题2: API返回403权限错误
**原因**: 用户没有相关权限
**解决**: 
- 为用户角色添加 `knowledge:strategy:query` 权限
- 或临时移除权限检查进行测试

### 问题3: 数据库连接错误
**原因**: 数据库表不存在或连接失败
**解决**: 
- 执行 `sql/knowledge_strategy.sql` 创建表结构
- 检查数据库连接配置

### 问题4: 前端数据不更新
**原因**: Vue响应式数据更新问题
**解决**: 
- 检查 `selectedStrategies.value` 是否正确更新
- 确认 `confirmStrategyConfig` 方法被正确调用

### 问题5: 策略模板数据为空
**原因**: 数据库中没有策略模板数据
**解决**: 
- 执行 `sql/insert_test_strategy_data.sql` 插入测试数据
- 或在管理界面手动添加策略模板

## 调试模式启用

1. **启用前端调试日志**
   - 前端代码中已添加 `console.log` 调试信息
   - 打开浏览器控制台查看详细日志

2. **启用后端调试日志**
   在 `application.yml` 中添加：
   ```yaml
   logging:
     level:
       com.ruoyi.knowledge: DEBUG
   ```

## 验证修复

1. **功能验证步骤**
   - 点击"配置知识库策略"按钮
   - 弹窗应显示策略类型标签页
   - 每个标签页应显示可选的策略模板
   - 选择策略后点击"确定"
   - 按钮文本应更新为"配置知识库策略 (N)"，其中N为选择的策略数量
   - 下方应显示选中的策略标签

2. **成功标志**
   - 浏览器控制台无错误信息
   - API请求返回200状态码
   - 策略数量正确显示
   - 策略标签正确显示

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 浏览器控制台的完整错误日志
2. 后端应用日志
3. 数据库中策略模板表的数据
4. 网络请求的详细信息（状态码、响应内容）
