<!DOCTYPE html>
<html>
<head>
    <title>策略API测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>策略API测试</h1>
    <button onclick="testStrategyTypes()">测试获取策略类型</button>
    <button onclick="testStrategyTemplates()">测试获取策略模板</button>
    
    <div id="result"></div>

    <script>
        const baseURL = 'http://localhost:8080'; // 根据你的后端端口调整
        
        async function testStrategyTypes() {
            try {
                console.log('测试获取策略类型...');
                const response = await axios.get(`${baseURL}/knowledge/strategy/types`);
                console.log('策略类型响应:', response.data);
                document.getElementById('result').innerHTML = '<h3>策略类型:</h3><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                console.error('获取策略类型失败:', error);
                document.getElementById('result').innerHTML = '<h3>错误:</h3><pre>' + error.message + '</pre>';
            }
        }
        
        async function testStrategyTemplates() {
            try {
                console.log('测试获取策略模板...');
                const response = await axios.get(`${baseURL}/knowledge/strategy/template/type/INITIALIZATION`);
                console.log('策略模板响应:', response.data);
                document.getElementById('result').innerHTML = '<h3>初始化策略模板:</h3><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                console.error('获取策略模板失败:', error);
                document.getElementById('result').innerHTML = '<h3>错误:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
