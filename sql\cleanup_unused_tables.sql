-- 清理无用的策略相关表格
-- 执行前请确保已备份重要数据

-- 删除旧的策略配置表（这个表结构与新设计不符）
DROP TABLE IF EXISTS `knowledge_strategy_config`;

-- 删除策略执行日志表（如果不需要的话）
-- DROP TABLE IF EXISTS `knowledge_strategy_execution_log`;

-- 保留以下表格（这些是我们需要的）：
-- knowledge_strategy_template - 策略模板表
-- knowledge_base_strategy_config - 知识库策略配置表  
-- knowledge_strategy_execution_log - 策略执行日志表

-- 查看剩余的策略相关表
SHOW TABLES LIKE '%strategy%';

-- 确认策略模板表的数据
SELECT COUNT(*) as template_count FROM knowledge_strategy_template;
SELECT strategy_type, COUNT(*) as count FROM knowledge_strategy_template GROUP BY strategy_type;
