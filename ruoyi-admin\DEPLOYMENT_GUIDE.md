# AI 智能问答模块部署指南

## 🚀 快速开始

### 1. 环境准备

确保您已经准备好以下环境：

- ✅ Java 8+
- ✅ MySQL 5.7+
- ✅ 阿里云 API 密钥
- ✅ 若依框架基础环境

### 2. 获取阿里云 API 密钥

1. 登录阿里云控制台
2. 开通 DashScope 服务
3. 获取 API Key
4. 设置环境变量：

```bash
export API-KEY=your-dashscope-api-key
```

### 3. 数据库配置

执行 SQL 脚本创建 AI 相关表：

```sql
-- 在MySQL中执行
source ruoyi-admin/src/main/resources/sql/ai_chat_tables.sql;
```

### 4. 启动后端服务

```bash
cd ruoyi-admin
mvn clean install
mvn spring-boot:run
```

### 5. 启动前端服务

```bash
cd ruoyi-ui/RuoYi-Vue3
npm install
npm run dev
```

## 🧪 功能测试

### 测试步骤

1. **访问系统**: http://localhost:80
2. **登录系统**: admin/admin123
3. **访问 AI 模块**: 菜单 -> 智能问答 -> 智能问答
4. **测试对话**: 发送消息测试 AI 响应

### API 测试

```bash
# 测试AI连接
curl -X GET "http://localhost:8080/ai/test/connection" \
  -H "Authorization: Bearer your-token"

# 测试简单对话
curl -X POST "http://localhost:8080/ai/test/chat" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer your-token" \
  -d "message=你好"
```

## 📋 配置检查清单

### 后端配置

- [ ] API 密钥已设置在环境变量中
- [ ] application.yml 中 AI 配置正确
- [ ] 数据库表已创建
- [ ] 菜单权限已配置
- [ ] 服务启动无错误

### 前端配置

- [ ] 路由配置正确
- [ ] API 接口地址正确
- [ ] 页面可以正常访问
- [ ] 消息发送接收正常

## 🔧 故障排除

### 常见问题及解决方案

#### 1. API 密钥相关

**问题**: API 调用失败，返回 401 错误
**解决**:

```bash
# 检查环境变量
echo $API-KEY

# 重新设置环境变量
export API-KEY=your-correct-api-key

# 重启应用
```

#### 2. 数据库连接问题

**问题**: 会话创建失败
**解决**:

```sql
-- 检查表是否存在
SHOW TABLES LIKE 'ai_%';

-- 检查表结构
DESC ai_chat_session;
DESC ai_chat_message;
```

#### 3. 权限问题

**问题**: 访问 AI 页面提示无权限
**解决**:

```sql
-- 检查菜单是否存在
SELECT * FROM sys_menu WHERE perms LIKE 'ai:%';

-- 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'ai:%';
```

#### 4. 网络连接问题

**问题**: AI 服务连接超时
**解决**:

```bash
# 测试网络连接
curl -I https://dashscope.aliyuncs.com/compatible-mode/v1

# 检查防火墙设置
# 确保服务器可以访问外网
```

## 📊 监控和日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/ruoyi.log

# 查看AI相关日志
tail -f logs/ruoyi.log | grep -i "ai\|langchain"

# 查看错误日志
tail -f logs/ruoyi.log | grep -i "error\|exception"
```

### 性能监控

- 响应时间统计
- Token 使用量监控
- 错误率统计
- 并发用户数监控

## 🔒 安全配置

### 1. API 密钥安全

```bash
# 使用环境变量（推荐）
export API-KEY=your-api-key

# 或使用配置文件（不推荐用于生产环境）
# 在application-prod.yml中配置
```

### 2. 权限控制

确保以下权限正确配置：

- `ai:chat:view` - 查看页面
- `ai:chat:send` - 发送消息
- `ai:chat:history` - 查看历史
- `ai:chat:session` - 管理会话

### 3. 数据安全

- 定期备份会话数据
- 敏感信息脱敏处理
- 访问日志记录

## 🚀 生产环境部署

### 1. 环境变量配置

```bash
# 生产环境配置
export SPRING_PROFILES_ACTIVE=prod
export API-KEY=your-production-api-key
export MYSQL_HOST=your-mysql-host
export MYSQL_PORT=3306
export MYSQL_DATABASE=your-database
export MYSQL_USERNAME=your-username
export MYSQL_PASSWORD=your-password
```

### 2. 应用打包

```bash
# 后端打包
cd ruoyi-admin
mvn clean package -Pprod

# 前端打包
cd ruoyi-ui/RuoYi-Vue3
npm run build:prod
```

### 3. 部署脚本

```bash
#!/bin/bash
# deploy.sh

# 停止旧服务
pkill -f ruoyi-admin

# 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 部署新版本
java -jar ruoyi-admin.jar --spring.profiles.active=prod

# 检查服务状态
sleep 10
curl -f http://localhost:8080/ai/test/connection || exit 1

echo "部署完成"
```

## 📈 扩展开发

### 1. 添加新的 AI 模型

在`AiConfig.java`中添加新的模型配置

### 2. 自定义提示词

修改`@SystemMessage`注解内容

### 3. 集成其他 AI 服务

实现新的`ChatLanguageModel`

## 📞 技术支持

如遇到问题，请检查：

1. 日志文件中的错误信息
2. 网络连接是否正常
3. API 密钥是否有效
4. 数据库配置是否正确
5. 权限设置是否完整

## 📝 更新日志

### v1.0.0 (当前版本)

- ✅ 集成 LangChain4j 框架
- ✅ 支持阿里云通义千问
- ✅ 实现流式输出
- ✅ 会话记忆和隔离
- ✅ 权限控制集成
- ✅ 数据持久化存储

### 后续计划

- 🔄 支持更多 AI 模型
- 📊 增强监控和统计
- 🎨 优化用户界面
- 🔧 性能优化
