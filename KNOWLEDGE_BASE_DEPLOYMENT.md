# 知识库管理系统部署指南

## 📋 概述

本文档介绍如何在RuoYi-Vue框架中部署知识库管理功能，包括知识库构建和知识库调优两个主要模块。

## 🚀 部署步骤

### 1. 数据库配置

#### 1.1 执行菜单权限SQL
```sql
-- 执行菜单配置脚本
source sql/knowledge_base_menu.sql;
```

#### 1.2 执行数据表结构SQL
```sql
-- 执行表结构和测试数据脚本
source sql/knowledge_base_tables.sql;
```

### 2. 后端配置

#### 2.1 确认文件结构
确保以下文件已正确放置：

**实体类 (Domain)**
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/domain/KnowledgeBase.java`
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/domain/KnowledgeDocument.java`
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/domain/KnowledgeFolder.java`

**数据访问层 (Mapper)**
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/mapper/KnowledgeDocumentMapper.java`
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/mapper/KnowledgeFolderMapper.java`
- `ruoyi-system/src/main/resources/mapper/knowledge/KnowledgeDocumentMapper.xml`
- `ruoyi-system/src/main/resources/mapper/knowledge/KnowledgeFolderMapper.xml`

**业务逻辑层 (Service)**
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/service/IKnowledgeDocumentService.java`
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/service/IKnowledgeFolderService.java`
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/service/impl/KnowledgeDocumentServiceImpl.java`
- `ruoyi-system/src/main/java/com/ruoyi/knowledge/service/impl/KnowledgeFolderServiceImpl.java`

**控制器层 (Controller)**
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/knowledge/KnowledgeBuildController.java`

#### 2.2 重启后端服务
```bash
# 停止服务
./stop.sh

# 重新编译
mvn clean package -Dmaven.test.skip=true

# 启动服务
./start.sh
```

### 3. 前端配置

#### 3.1 确认前端文件结构
确保以下文件已正确放置：

**页面组件**
- `ruoyi-ui/RuoYi-Vue3/src/views/knowledge/build/index.vue`
- `ruoyi-ui/RuoYi-Vue3/src/views/knowledge/optimize/index.vue`

**API接口**
- `ruoyi-ui/RuoYi-Vue3/src/api/knowledge/build.js`

#### 3.2 重启前端服务
```bash
cd ruoyi-ui/RuoYi-Vue3
npm run dev
```

## 🎯 功能特性

### 知识库构建模块

#### 主要功能
1. **文档管理**
   - 文档上传（支持 .md, .docx, .pdf, .txt 格式）
   - 文档查看、编辑、删除
   - 文档内容搜索
   - 文档处理状态跟踪

2. **文件夹管理**
   - 创建、编辑、删除文件夹
   - 树形结构展示
   - 文件夹层级管理

3. **搜索功能**
   - 全文搜索
   - 按文件夹筛选
   - 按文档类型筛选

#### 权限控制
- `knowledge:build:query` - 查询权限
- `knowledge:build:add` - 新增权限
- `knowledge:build:edit` - 编辑权限
- `knowledge:build:remove` - 删除权限
- `knowledge:build:upload` - 上传权限
- `knowledge:build:export` - 导出权限

### 知识库调优模块
- 当前为占位页面，后续可扩展具体功能

## 📊 数据库表结构

### knowledge_base (知识库表)
- 存储知识库基本信息
- 支持分类和权限控制

### knowledge_folder (知识库文件夹表)
- 树形结构存储
- 支持多层级文件夹
- 统计信息维护

### knowledge_document (知识库文档表)
- 文档详细信息存储
- 支持全文搜索
- 访问统计功能

## 🔧 使用说明

### 1. 访问系统
1. 登录RuoYi管理系统
2. 在左侧菜单中找到"知识库管理"
3. 点击"知识库构建"进入主界面

### 2. 文件夹操作
- **创建文件夹**: 点击左侧"新建文件夹"按钮
- **编辑文件夹**: 点击文件夹旁的"编辑"按钮
- **删除文件夹**: 点击文件夹旁的"删除"按钮

### 3. 文档操作
- **上传文档**: 点击"上传文档"按钮，选择文件上传
- **批量上传**: 点击"批量导入"按钮，选择多个文件
- **搜索文档**: 在搜索框中输入关键词
- **查看文档**: 点击文档名称或"查看"按钮
- **编辑文档**: 点击"编辑"按钮修改文档信息
- **处理文档**: 点击"处理"按钮进行文档内容处理
- **删除文档**: 点击"删除"按钮删除文档

## 🛠️ 扩展开发

### 1. 文档内容解析
可以集成以下库来解析不同格式的文档：
- **Apache POI**: 处理Word、Excel文档
- **PDFBox**: 处理PDF文档
- **CommonMark**: 处理Markdown文档

### 2. 向量化存储
集成向量数据库支持AI问答：
- **Elasticsearch**: 全文搜索和向量搜索
- **Milvus**: 专业向量数据库
- **Pinecone**: 云端向量数据库

### 3. 文档预览
添加在线文档预览功能：
- **Office Online**: 在线预览Office文档
- **PDF.js**: 在线预览PDF文档
- **Monaco Editor**: 在线编辑代码和文本

## 🐛 故障排除

### 1. 常见问题

**菜单不显示**
- 检查是否执行了菜单SQL脚本
- 确认用户角色是否有相应权限
- 重启后端服务

**文件上传失败**
- 检查文件大小是否超过限制（默认10MB）
- 确认文件格式是否支持
- 检查上传目录权限

**搜索功能异常**
- 确认数据库是否支持全文搜索
- 检查索引是否正确创建

### 2. 日志查看
```bash
# 查看后端日志
tail -f logs/ruoyi.log | grep -i "knowledge"

# 查看前端控制台
# 打开浏览器开发者工具查看Console和Network面板
```

## 📞 技术支持

如遇到问题，请检查：
1. 数据库连接是否正常
2. 文件权限是否正确
3. 依赖包是否完整
4. 配置文件是否正确

建议在测试环境中先进行完整测试，确认功能正常后再部署到生产环境。
