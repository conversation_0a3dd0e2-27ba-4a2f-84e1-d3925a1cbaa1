package com.ruoyi.knowledge.config;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.collection.CreateCollectionParam;
import io.milvus.param.collection.FieldType;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.index.CreateIndexParam;
import io.milvus.param.index.HasIndexParam;
import io.milvus.common.clientenum.ConsistencyLevelEnum;
import io.milvus.grpc.DataType;
import io.milvus.grpc.MetricType;
import io.milvus.grpc.IndexType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Arrays;

/**
 * Milvus向量数据库配置类
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Configuration
public class MilvusConfig {

    private static final Logger logger = LoggerFactory.getLogger(MilvusConfig.class);

    @Value("${milvus.host:localhost}")
    private String milvusHost;

    @Value("${milvus.port:19530}")
    private int milvusPort;

    @Value("${milvus.username:}")
    private String milvusUsername;

    @Value("${milvus.password:}")
    private String milvusPassword;

    @Value("${milvus.database:default}")
    private String milvusDatabase;

    @Value("${milvus.collection.name:knowledge_base_vectors}")
    private String collectionName;

    @Value("${milvus.vector.dimension:384}")
    private int vectorDimension;

    /**
     * Milvus客户端Bean
     */
    @Bean
    public MilvusServiceClient milvusClient() {
        ConnectParam.Builder connectBuilder = ConnectParam.newBuilder()
                .withHost(milvusHost)
                .withPort(milvusPort)
                .withDatabaseName(milvusDatabase);

        // 如果有用户名和密码则设置认证
        if (milvusUsername != null && !milvusUsername.trim().isEmpty()) {
            connectBuilder.withUsername(milvusUsername);
        }
        if (milvusPassword != null && !milvusPassword.trim().isEmpty()) {
            connectBuilder.withPassword(milvusPassword);
        }

        MilvusServiceClient client = new MilvusServiceClient(connectBuilder.build());
        logger.info("Milvus客户端连接成功: {}:{}", milvusHost, milvusPort);
        return client;
    }

    /**
     * 初始化Milvus集合和索引
     */
    @PostConstruct
    public void initializeMilvus() {
        try {
            MilvusServiceClient client = milvusClient();
            
            // 检查集合是否存在
            HasCollectionParam hasCollectionParam = HasCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();
            
            boolean hasCollection = client.hasCollection(hasCollectionParam).getData();
            
            if (!hasCollection) {
                createCollection(client);
                createIndex(client);
                logger.info("Milvus集合 {} 创建成功", collectionName);
            } else {
                logger.info("Milvus集合 {} 已存在", collectionName);
            }
            
        } catch (Exception e) {
            logger.error("初始化Milvus失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建集合
     */
    private void createCollection(MilvusServiceClient client) {
        // 定义字段
        FieldType idField = FieldType.newBuilder()
                .withName("id")
                .withDataType(DataType.VarChar)
                .withMaxLength(255)
                .withPrimaryKey(true)
                .withAutoID(false)
                .build();

        FieldType vectorField = FieldType.newBuilder()
                .withName("vector")
                .withDataType(DataType.FloatVector)
                .withDimension(vectorDimension)
                .build();

        FieldType textField = FieldType.newBuilder()
                .withName("text")
                .withDataType(DataType.VarChar)
                .withMaxLength(65535)
                .build();

        FieldType knowledgeBaseIdField = FieldType.newBuilder()
                .withName("knowledge_base_id")
                .withDataType(DataType.VarChar)
                .withMaxLength(50)
                .build();

        FieldType documentIdField = FieldType.newBuilder()
                .withName("document_id")
                .withDataType(DataType.VarChar)
                .withMaxLength(50)
                .build();

        FieldType documentNameField = FieldType.newBuilder()
                .withName("document_name")
                .withDataType(DataType.VarChar)
                .withMaxLength(255)
                .build();

        FieldType documentTypeField = FieldType.newBuilder()
                .withName("document_type")
                .withDataType(DataType.VarChar)
                .withMaxLength(50)
                .build();

        // 创建集合
        CreateCollectionParam createCollectionParam = CreateCollectionParam.newBuilder()
                .withCollectionName(collectionName)
                .withDescription("知识库向量存储集合")
                .withShardsNum(2)
                .withConsistencyLevel(ConsistencyLevelEnum.STRONG)
                .withFieldTypes(Arrays.asList(
                        idField, vectorField, textField, 
                        knowledgeBaseIdField, documentIdField, 
                        documentNameField, documentTypeField
                ))
                .build();

        client.createCollection(createCollectionParam);
    }

    /**
     * 创建索引
     */
    private void createIndex(MilvusServiceClient client) {
        // 检查向量字段是否已有索引
        HasIndexParam hasIndexParam = HasIndexParam.newBuilder()
                .withCollectionName(collectionName)
                .withFieldName("vector")
                .build();

        boolean hasIndex = client.hasIndex(hasIndexParam).getData();
        
        if (!hasIndex) {
            // 创建向量字段索引
            CreateIndexParam createIndexParam = CreateIndexParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withFieldName("vector")
                    .withIndexType(IndexType.IVF_FLAT)
                    .withMetricType(MetricType.COSINE)
                    .withExtraParam("{\"nlist\":1024}")
                    .build();

            client.createIndex(createIndexParam);
            logger.info("为集合 {} 的向量字段创建索引成功", collectionName);
        }
    }

    // Getter方法
    public String getCollectionName() {
        return collectionName;
    }

    public int getVectorDimension() {
        return vectorDimension;
    }
}
