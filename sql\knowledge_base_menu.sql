-- 知识库管理菜单 SQL
-- 插入知识库管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('知识库管理', 0, 5, 'knowledge', NULL, 1, 0, 'M', '0', '0', NULL, 'documentation', 'admin', sysdate(), '', NULL, '知识库管理目录');

-- 获取刚插入的父菜单ID
SET @parent_menu_id = LAST_INSERT_ID();

-- 插入子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('知识库构建', @parent_menu_id, 1, 'build', 'knowledge/build/index', 1, 0, 'C', '0', '0', 'knowledge:build:view', 'edit', 'admin', sysdate(), '', NULL, '知识库构建页面'),
('知识库调优', @parent_menu_id, 2, 'optimize', 'knowledge/optimize/index', 1, 0, 'C', '0', '0', 'knowledge:optimize:view', 'skill', 'admin', sysdate(), '', NULL, '知识库调优页面');

-- 获取知识库构建菜单ID
SET @build_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'build' AND parent_id = @parent_menu_id);

-- 插入知识库构建按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('知识库查询', @build_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:query', '#', 'admin', sysdate(), '', NULL, ''),
('知识库新增', @build_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:add', '#', 'admin', sysdate(), '', NULL, ''),
('知识库修改', @build_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:edit', '#', 'admin', sysdate(), '', NULL, ''),
('知识库删除', @build_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:remove', '#', 'admin', sysdate(), '', NULL, ''),
('文档上传', @build_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:upload', '#', 'admin', sysdate(), '', NULL, ''),
('文档导入', @build_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:import', '#', 'admin', sysdate(), '', NULL, ''),
('知识库导出', @build_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'knowledge:build:export', '#', 'admin', sysdate(), '', NULL, '');

-- 获取知识库调优菜单ID
SET @optimize_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'optimize' AND parent_id = @parent_menu_id);

-- 插入知识库调优按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('调优查询', @optimize_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'knowledge:optimize:query', '#', 'admin', sysdate(), '', NULL, ''),
('调优配置', @optimize_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'knowledge:optimize:config', '#', 'admin', sysdate(), '', NULL, ''),
('性能分析', @optimize_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'knowledge:optimize:analyze', '#', 'admin', sysdate(), '', NULL, ''),
('调优执行', @optimize_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'knowledge:optimize:execute', '#', 'admin', sysdate(), '', NULL, ''),
('调优导出', @optimize_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'knowledge:optimize:export', '#', 'admin', sysdate(), '', NULL, '');

-- 为管理员角色分配知识库管理相关权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'knowledge:%' OR (parent_id IN (SELECT menu_id FROM sys_menu WHERE path = 'knowledge'));

COMMIT;
