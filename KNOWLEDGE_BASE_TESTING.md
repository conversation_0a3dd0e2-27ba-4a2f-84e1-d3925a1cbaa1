# 知识库管理功能测试指南

## 🧪 测试前准备

### 1. 确认部署完成
- ✅ 数据库SQL脚本已执行
- ✅ 后端服务已重启
- ✅ 前端服务已重启
- ✅ 用户具有相应权限

### 2. 登录系统
1. 使用管理员账号登录RuoYi系统
2. 确认左侧菜单中显示"知识库管理"

## 🔍 功能测试步骤

### 测试1: 菜单访问测试
**目标**: 验证菜单配置是否正确

**步骤**:
1. 登录系统后，查看左侧菜单
2. 找到"知识库管理"主菜单
3. 展开后应显示"知识库构建"和"知识库调优"子菜单
4. 点击"知识库构建"

**预期结果**:
- 菜单正常显示
- 页面成功加载，显示知识库构建界面
- 左侧显示文件夹树，右侧显示文档列表

### 测试2: 文件夹功能测试
**目标**: 验证文件夹管理功能

**步骤**:
1. 在知识库构建页面，查看左侧文件夹树
2. 应该看到预置的测试文件夹：
   - 产品文档
     - 需求文档
     - 设计文档
   - 技术文档
     - API文档
     - 部署文档

**预期结果**:
- 文件夹树正常显示
- 可以展开/收起文件夹
- 点击文件夹时右侧文档列表会更新

### 测试3: 文档列表测试
**目标**: 验证文档显示功能

**步骤**:
1. 查看右侧文档列表
2. 应该看到预置的测试文档：
   - 产品需求文档.docx
   - API接口文档.md
   - 系统部署手册.pdf

**预期结果**:
- 文档列表正常显示
- 显示文档名称、类型、大小、更新时间、状态等信息
- 操作按钮（查看、编辑、处理、删除）正常显示

### 测试4: 搜索功能测试
**目标**: 验证搜索功能

**步骤**:
1. 在搜索框中输入"API"
2. 点击搜索按钮或按回车

**预期结果**:
- 搜索结果只显示包含"API"关键词的文档
- 搜索结果高亮显示匹配内容

### 测试5: 文档操作测试
**目标**: 验证文档操作功能

**步骤**:
1. 点击某个文档的"查看"按钮
2. 点击"处理"按钮
3. 尝试删除操作（建议先备份数据）

**预期结果**:
- 查看功能正常响应
- 处理功能执行成功，文档状态更新
- 删除功能正常工作

### 测试6: 文件上传测试
**目标**: 验证文件上传功能

**步骤**:
1. 点击"上传文档"按钮
2. 选择一个支持的文件格式（.md, .docx, .pdf, .txt）
3. 选择目标文件夹
4. 点击上传

**预期结果**:
- 上传界面正常显示
- 文件上传成功
- 文档列表中显示新上传的文件

## 🐛 常见问题排查

### 问题1: 菜单不显示
**可能原因**:
- SQL脚本未执行
- 用户权限不足
- 后端服务未重启

**解决方案**:
```sql
-- 检查菜单是否存在
SELECT * FROM sys_menu WHERE menu_name = '知识库管理';

-- 检查用户权限
SELECT * FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE perms LIKE 'knowledge:%'
);
```

### 问题2: 页面加载失败
**可能原因**:
- 前端路由配置问题
- API接口不可访问
- 网络连接问题

**解决方案**:
1. 检查浏览器控制台错误信息
2. 检查Network面板API请求状态
3. 确认后端服务正常运行

### 问题3: 文件上传失败
**可能原因**:
- 文件大小超限（默认10MB）
- 文件格式不支持
- 上传目录权限问题

**解决方案**:
1. 检查文件大小和格式
2. 确认上传目录存在且有写权限
3. 查看后端日志错误信息

### 问题4: 搜索功能异常
**可能原因**:
- 数据库全文索引问题
- 搜索关键词特殊字符
- 数据库连接问题

**解决方案**:
```sql
-- 检查全文索引
SHOW INDEX FROM knowledge_document WHERE Key_name = 'idx_content';

-- 测试简单搜索
SELECT * FROM knowledge_document WHERE name LIKE '%test%';
```

## 📊 性能测试

### 测试场景1: 大量文档加载
1. 上传100+个文档
2. 测试列表加载速度
3. 测试搜索响应时间

### 测试场景2: 深层文件夹结构
1. 创建5层以上的文件夹结构
2. 测试树形展示性能
3. 测试文件夹切换速度

### 测试场景3: 并发操作
1. 多用户同时上传文档
2. 同时进行搜索操作
3. 测试系统稳定性

## 📝 测试报告模板

### 测试环境
- 操作系统: 
- 浏览器: 
- 数据库版本: 
- Java版本: 

### 测试结果
| 测试项目 | 状态 | 备注 |
|---------|------|------|
| 菜单访问 | ✅/❌ |  |
| 文件夹功能 | ✅/❌ |  |
| 文档列表 | ✅/❌ |  |
| 搜索功能 | ✅/❌ |  |
| 文档操作 | ✅/❌ |  |
| 文件上传 | ✅/❌ |  |

### 发现的问题
1. 问题描述:
   - 重现步骤:
   - 预期结果:
   - 实际结果:
   - 解决方案:

## 🚀 上线前检查清单

- [ ] 所有核心功能测试通过
- [ ] 权限控制正常工作
- [ ] 文件上传限制合理
- [ ] 搜索功能响应正常
- [ ] 错误处理机制完善
- [ ] 日志记录完整
- [ ] 数据备份策略确定
- [ ] 用户培训材料准备

## 📞 技术支持

如果在测试过程中遇到问题：

1. **查看日志**:
   ```bash
   tail -f logs/ruoyi.log | grep -i "knowledge"
   ```

2. **检查数据库**:
   ```sql
   SELECT COUNT(*) FROM knowledge_document;
   SELECT COUNT(*) FROM knowledge_folder;
   ```

3. **验证权限**:
   ```sql
   SELECT m.menu_name, m.perms 
   FROM sys_menu m 
   WHERE m.perms LIKE 'knowledge:%';
   ```

建议在生产环境部署前，在测试环境中完成所有测试项目，确保功能稳定可靠。
