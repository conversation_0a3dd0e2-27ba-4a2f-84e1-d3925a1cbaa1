package com.ruoyi.knowledge.config;

import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.allminilml6v2.AllMiniLmL6V2EmbeddingModel;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.redis.RedisEmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import com.ruoyi.knowledge.store.MilvusEmbeddingStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 知识库RAG配置类
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Configuration
public class KnowledgeRagConfig {

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${knowledge.embedding.store.type:milvus}")
    private String storeType;

    @Autowired(required = false)
    private MilvusEmbeddingStore milvusEmbeddingStore;

    /**
     * 嵌入模型Bean
     * 使用本地的AllMiniLmL6V2模型，无需API调用
     */
    @Bean
    public EmbeddingModel embeddingModel() {
        return new AllMiniLmL6V2EmbeddingModel();
    }

    /**
     * 向量存储Bean
     * 支持Redis和内存两种存储方式
     */
    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        if ("redis".equalsIgnoreCase(storeType)) {
            // 使用Redis存储（需要RediSearch模块）
            RedisEmbeddingStore.Builder builder = RedisEmbeddingStore.builder()
                    .host(redisHost)
                    .port(redisPort)
                    .dimension(384) // AllMiniLmL6V2模型的向量维度是384
                    .indexName("knowledge_base_index"); // 指定索引名称

            // 如果有密码则设置密码
            if (redisPassword != null && !redisPassword.trim().isEmpty()) {
                builder.password(redisPassword);
            }

            return builder.build();
        } else {
            // 使用内存存储（默认，不需要Redis模块）
            return new InMemoryEmbeddingStore<>();
        }
    }

    /**
     * 内容检索器Bean
     */
    @Bean
    public ContentRetriever contentRetriever(EmbeddingStore<TextSegment> embeddingStore,
            EmbeddingModel embeddingModel) {
        return EmbeddingStoreContentRetriever.builder()
                .embeddingStore(embeddingStore)
                .embeddingModel(embeddingModel)
                .maxResults(5) // 最多返回5个相关结果
                .minScore(0.6) // 最小相似度分数
                .build();
    }

    /**
     * 文档分割器Bean
     * 用于将长文档分割成小段落
     */
    @Bean
    public DocumentSplitter documentSplitter() {
        return DocumentSplitters.recursive(
                500, // 每段最大字符数
                50 // 段落间重叠字符数
        );
    }
}
