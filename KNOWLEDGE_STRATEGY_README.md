# 知识库策略管理系统

## 🎯 项目概述

本项目为知识库管理系统添加了完整的策略管理功能，允许知识库管理员制定和配置各种知识处理策略，支持多策略并行执行，提供灵活的知识库构建和管理能力。

## ✨ 核心功能

### 1. 策略类型支持

- **🚀 知识库初始化策略** - 控制知识库的创建和初始化过程
- **📄 知识段落切分策略** - 控制文档如何分割成段落
- **🏷️ 知识标签策略** - 为知识段落自动生成标签
- **📝 知识归纳总结策略** - 对知识内容进行归纳总结
- **🔗 相关知识关联策略** - 建立知识之间的关联关系

### 2. 策略管理功能

- ✅ 策略模板的创建、编辑、删除
- ✅ 策略配置的JSON格式验证
- ✅ 默认策略设置和推荐
- ✅ 策略启用/禁用控制
- ✅ 策略执行优先级管理

### 3. 知识库构建增强

- ✅ 可视化策略选择界面
- ✅ 多策略组合配置
- ✅ 策略执行状态跟踪
- ✅ 个性化策略参数设置

### 4. 策略执行引擎

- ✅ 串行和并行执行支持
- ✅ 策略执行日志记录
- ✅ 异常处理和错误恢复
- ✅ 执行结果统计分析

## 🏗️ 系统架构

### 后端架构

```
知识库策略管理系统
├── 策略接口层 (KnowledgeStrategy)
├── 策略实现层 (各种具体策略)
├── 策略工厂 (StrategyFactory)
├── 策略执行器 (StrategyExecutor)
├── 服务层 (Service)
├── 数据访问层 (Mapper)
└── 数据库层 (MySQL)
```

### 前端架构

```
前端界面
├── 知识库构建页面 (集成策略选择)
├── 策略管理页面 (策略模板管理)
├── 策略配置弹窗 (可视化配置)
└── API接口层 (与后端通信)
```

## 📊 数据库设计

### 核心表结构

1. **knowledge_strategy_template** - 策略模板表
   - 存储策略模板的基本信息和配置
   - 支持策略类型分类和排序

2. **knowledge_base_strategy_config** - 知识库策略配置表
   - 存储知识库与策略模板的关联关系
   - 支持个性化配置覆盖

3. **knowledge_strategy_execution_log** - 策略执行日志表
   - 记录策略执行的详细日志
   - 支持性能分析和问题排查

## 🚀 快速开始

### 1. 环境要求

- Java 8+
- MySQL 5.7+
- Node.js 14+
- Vue 3.x

### 2. 部署步骤

```bash
# 1. 执行数据库脚本
mysql -u root -p your_database < sql/knowledge_strategy.sql

# 2. 编译后端项目
mvn clean package -Dmaven.test.skip=true

# 3. 启动后端服务
./start.sh

# 4. 启动前端服务
cd ruoyi-ui/RuoYi-Vue3
npm run dev
```

### 3. 功能使用

1. **策略模板管理**
   - 访问 "知识库管理" -> "策略管理"
   - 创建和配置策略模板

2. **知识库创建**
   - 在知识库构建页面选择文档
   - 点击"配置知识库策略"选择策略
   - 完成知识库创建

## 🔧 配置说明

### 策略配置示例

#### 初始化策略配置
```json
{
  "enableVectorization": true,
  "enableMetadata": true,
  "batchSize": 100,
  "autoProcessDocuments": true,
  "createIndexes": true
}
```

#### 段落切分策略配置
```json
{
  "maxChunkSize": 500,
  "chunkOverlap": 50,
  "separators": ["\\n\\n", "\\n", " ", ""]
}
```

## 🧪 测试

### 运行单元测试

```bash
# 运行策略功能测试
mvn test -Dtest=StrategyTest

# 运行所有测试
mvn test
```

### 测试覆盖

- ✅ 策略执行功能测试
- ✅ 配置验证测试
- ✅ 异常处理测试
- ✅ 并发执行测试

## 📈 性能特性

- **高并发支持** - 支持多策略并行执行
- **内存优化** - 策略实例缓存和复用
- **执行监控** - 详细的执行日志和性能统计
- **错误恢复** - 完善的异常处理机制

## 🔒 安全特性

- **权限控制** - 基于角色的策略管理权限
- **配置验证** - 严格的策略配置格式验证
- **执行隔离** - 策略执行的安全隔离
- **日志审计** - 完整的操作日志记录

## 🛠️ 扩展开发

### 添加新策略类型

1. 创建策略接口
```java
public interface NewStrategy extends KnowledgeStrategy<InputType, OutputType> {
    // 策略接口定义
}
```

2. 实现策略类
```java
@Component
public class DefaultNewStrategy extends AbstractKnowledgeStrategy<InputType, OutputType> 
        implements NewStrategy {
    // 策略实现
}
```

3. 更新策略类型枚举
```java
public enum StrategyType {
    NEW_STRATEGY("NEW_STRATEGY", "新策略", "新策略描述");
}
```

### 自定义策略配置

策略配置支持JSON格式，可以根据需要添加各种参数：

```json
{
  "customParam1": "value1",
  "customParam2": 123,
  "customParam3": true,
  "nestedConfig": {
    "subParam": "subValue"
  }
}
```

## 📚 API文档

### 策略管理API

- `GET /knowledge/strategy/types` - 获取策略类型列表
- `GET /knowledge/strategy/template/list` - 查询策略模板列表
- `POST /knowledge/strategy/template` - 创建策略模板
- `PUT /knowledge/strategy/template` - 更新策略模板
- `DELETE /knowledge/strategy/template/{ids}` - 删除策略模板

### 知识库构建API

- `POST /knowledge/build/knowledge-base/create` - 创建知识库（支持策略配置）

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](https://docs.example.com)

---

**🎉 感谢使用知识库策略管理系统！**
