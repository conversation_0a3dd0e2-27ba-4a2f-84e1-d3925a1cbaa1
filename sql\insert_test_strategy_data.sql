-- 插入测试策略模板数据
-- 如果数据库中没有策略模板数据，可以执行这个脚本

-- 清理现有数据（可选）
-- DELETE FROM knowledge_strategy_template;

-- 插入策略模板数据
INSERT INTO `knowledge_strategy_template` (`name`, `description`, `strategy_type`, `config_json`, `is_default`, `is_enabled`, `sort_order`, `creator_name`, `create_by`, `create_time`) VALUES
('默认初始化策略', '知识库的默认初始化策略，包含基础的创建和配置流程', 'INITIALIZATION', '{"enableVectorization": true, "enableMetadata": true, "batchSize": 100}', '1', '1', 1, 'system', 'system', NOW()),
('快速初始化策略', '快速初始化策略，适合小型知识库', 'INITIALIZATION', '{"enableVectorization": false, "enableMetadata": true, "batchSize": 50}', '0', '1', 2, 'system', 'system', NOW()),

('递归文档分割策略', '使用递归方式分割文档，适合大多数文档类型', 'SEGMENTATION', '{"maxChunkSize": 500, "chunkOverlap": 50, "separators": ["\\n\\n", "\\n", " ", ""]}', '1', '1', 1, 'system', 'system', NOW()),
('固定长度分割策略', '按固定长度分割文档', 'SEGMENTATION', '{"maxChunkSize": 300, "chunkOverlap": 30}', '0', '1', 2, 'system', 'system', NOW()),

('智能标签生成策略', '基于AI的智能标签生成策略', 'TAGGING', '{"maxTags": 5, "useAI": true, "keywordExtraction": true}', '1', '1', 1, 'system', 'system', NOW()),
('关键词标签策略', '基于关键词提取的标签策略', 'TAGGING', '{"maxTags": 3, "useAI": false, "keywordExtraction": true}', '0', '1', 2, 'system', 'system', NOW()),

('AI摘要生成策略', '使用AI生成知识段落的摘要', 'SUMMARIZATION', '{"maxSummaryLength": 200, "useAI": true, "language": "zh"}', '1', '1', 1, 'system', 'system', NOW()),
('简单摘要策略', '提取文档前几句作为摘要', 'SUMMARIZATION', '{"maxSummaryLength": 100, "useAI": false, "sentenceCount": 3}', '0', '1', 2, 'system', 'system', NOW()),

('语义相似度关联策略', '基于语义相似度建立知识关联', 'ASSOCIATION', '{"similarityThreshold": 0.7, "maxAssociations": 10, "useEmbedding": true}', '1', '1', 1, 'system', 'system', NOW()),
('关键词关联策略', '基于关键词匹配建立知识关联', 'ASSOCIATION', '{"similarityThreshold": 0.5, "maxAssociations": 5, "useEmbedding": false}', '0', '1', 2, 'system', 'system', NOW());

-- 查询插入的数据
SELECT * FROM knowledge_strategy_template ORDER BY strategy_type, sort_order;
