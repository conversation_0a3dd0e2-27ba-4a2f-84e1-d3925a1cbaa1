-- 知识库管理相关表结构

-- 1. 知识库表
DROP TABLE IF EXISTS `knowledge_base`;
CREATE TABLE `knowledge_base` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '知识库ID',
  `name` varchar(100) NOT NULL COMMENT '知识库名称',
  `description` text COMMENT '知识库描述',
  `folder_id` bigint DEFAULT NULL COMMENT '所属目录ID',
  `type` varchar(50) DEFAULT 'general' COMMENT '知识库类型',
  `status` char(1) DEFAULT '0' COMMENT '知识库状态（0正常 1停用）',
  `document_count` bigint DEFAULT '0' COMMENT '文档数量',
  `size` bigint DEFAULT '0' COMMENT '知识库大小（字节）',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(30) NOT NULL COMMENT '创建者名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库表';

-- 2. 知识库文件夹表
DROP TABLE IF EXISTS `knowledge_folder`;
CREATE TABLE `knowledge_folder` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件夹ID',
  `name` varchar(100) NOT NULL COMMENT '文件夹名称',
  `description` text COMMENT '文件夹描述',
  `parent_id` bigint DEFAULT '0' COMMENT '父文件夹ID',
  `knowledge_base_id` bigint DEFAULT NULL COMMENT '所属知识库ID',
  `path` varchar(500) DEFAULT '' COMMENT '文件夹路径',
  `level` int DEFAULT '0' COMMENT '文件夹层级',
  `order_num` int DEFAULT '0' COMMENT '排序号',
  `status` char(1) DEFAULT '0' COMMENT '文件夹状态（0正常 1停用）',
  `is_public` char(1) DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  `document_count` bigint DEFAULT '0' COMMENT '文档数量',
  `sub_folder_count` bigint DEFAULT '0' COMMENT '子文件夹数量',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(30) NOT NULL COMMENT '创建者名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_order_num` (`order_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库文件夹表';

-- 3. 知识库文档表
DROP TABLE IF EXISTS `knowledge_document`;
CREATE TABLE `knowledge_document` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `name` varchar(200) NOT NULL COMMENT '文档名称',
  `title` varchar(200) DEFAULT NULL COMMENT '文档标题',
  `content` longtext COMMENT '文档内容',
  `summary` text COMMENT '文档摘要',
  `knowledge_base_id` bigint DEFAULT NULL COMMENT '所属知识库ID',
  `folder_id` bigint DEFAULT NULL COMMENT '所属目录ID',
  `type` varchar(50) DEFAULT 'document' COMMENT '文档类型',
  `format` varchar(20) DEFAULT NULL COMMENT '文档格式',
  `size` bigint DEFAULT '0' COMMENT '文档大小（字节）',
  `status` char(1) DEFAULT '0' COMMENT '文档状态（0正常 1停用 2处理中）',
  `process_status` char(1) DEFAULT '0' COMMENT '处理状态（0未处理 1已处理 2处理失败）',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `is_public` char(1) DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  `view_count` bigint DEFAULT '0' COMMENT '访问次数',
  `download_count` bigint DEFAULT '0' COMMENT '下载次数',
  `last_access_time` datetime DEFAULT NULL COMMENT '最后访问时间',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(30) NOT NULL COMMENT '创建者名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_name` (`name`),
  FULLTEXT KEY `idx_content` (`content`,`title`,`summary`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库文档表';

-- 插入测试数据
-- 插入测试文件夹
INSERT INTO `knowledge_folder` (`name`, `description`, `parent_id`, `knowledge_base_id`, `path`, `level`, `order_num`, `status`, `is_public`, `document_count`, `sub_folder_count`, `creator_id`, `creator_name`, `create_by`, `create_time`) VALUES
('产品文档', '产品相关文档', 0, NULL, '/产品文档', 1, 1, '0', '1', 0, 2, 1, 'admin', 'admin', sysdate()),
('需求文档', '产品需求文档', 1, NULL, '/产品文档/需求文档', 2, 1, '0', '1', 0, 0, 1, 'admin', 'admin', sysdate()),
('设计文档', '产品设计文档', 1, NULL, '/产品文档/设计文档', 2, 2, '0', '1', 0, 0, 1, 'admin', 'admin', sysdate()),
('技术文档', '技术相关文档', 0, NULL, '/技术文档', 1, 2, '0', '1', 0, 2, 1, 'admin', 'admin', sysdate()),
('API文档', 'API接口文档', 4, NULL, '/技术文档/API文档', 2, 1, '0', '1', 0, 0, 1, 'admin', 'admin', sysdate()),
('部署文档', '系统部署文档', 4, NULL, '/技术文档/部署文档', 2, 2, '0', '1', 0, 0, 1, 'admin', 'admin', sysdate());

-- 插入测试文档
INSERT INTO `knowledge_document` (`name`, `title`, `content`, `summary`, `knowledge_base_id`, `folder_id`, `type`, `format`, `size`, `status`, `process_status`, `file_path`, `file_url`, `tags`, `version`, `is_public`, `view_count`, `download_count`, `creator_id`, `creator_name`, `create_by`, `create_time`) VALUES
('产品需求文档.docx', '产品需求规格说明书', '这是一个产品需求文档的示例内容...', '产品需求规格说明书，包含功能需求、非功能需求等', NULL, 2, 'word', 'docx', 2621440, '0', '1', '/upload/2024/07/10/product_requirement.docx', '/upload/2024/07/10/product_requirement.docx', '产品,需求,规格', '1.0', '1', 15, 3, 1, 'admin', 'admin', sysdate()),
('API接口文档.md', 'REST API 接口文档', '# API接口文档\n\n## 用户管理接口\n\n### 获取用户列表\n\n**接口地址：** GET /api/users\n\n**请求参数：**\n\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|------|\n| page | int | 否 | 页码 |\n| size | int | 否 | 每页数量 |\n\n**响应示例：**\n\n```json\n{\n  "code": 200,\n  "data": {\n    "list": [],\n    "total": 0\n  }\n}\n```', 'REST API接口文档，包含用户管理、权限管理等接口说明', NULL, 5, 'markdown', 'md', 1048576, '0', '1', '/upload/2024/07/10/api_doc.md', '/upload/2024/07/10/api_doc.md', 'API,接口,文档', '1.2', '1', 28, 8, 1, 'admin', 'admin', sysdate()),
('系统部署手册.pdf', '系统部署操作手册', NULL, '系统部署操作手册，包含环境准备、安装步骤、配置说明等', NULL, 6, 'pdf', 'pdf', 5242880, '0', '0', '/upload/2024/07/10/deploy_manual.pdf', '/upload/2024/07/10/deploy_manual.pdf', '部署,手册,运维', '1.0', '0', 5, 2, 1, 'admin', 'admin', sysdate());

COMMIT;
