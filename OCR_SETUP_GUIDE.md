# OCR图片文本提取配置指南

## 概述

本系统已集成Tesseract OCR功能，支持从上传的图片中提取文本内容并进行向量化处理，使图片能够成功构建到知识库中。

## 功能特性

- 🖼️ **多格式支持**: 支持 JPG、JPEG、PNG、GIF、BMP 等常见图片格式
- 🔤 **多语言识别**: 支持中文简体、英文等多种语言的文本识别
- 🧠 **智能向量化**: 提取的文本内容自动进行向量化处理
- 📚 **知识库集成**: 图片文本内容可被搜索和检索
- 🔄 **自动降级**: OCR失败时使用图片基本信息作为文本内容

## 安装配置

### 1. 安装Tesseract OCR

#### Windows系统
1. 下载Tesseract安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径（通常是 `C:\Program Files\Tesseract-OCR`）
3. 将安装路径添加到系统环境变量PATH中
4. 下载中文语言包：
   - 下载 `chi_sim.traineddata`（简体中文）
   - 放置到 `tessdata` 目录下

#### Linux系统
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# CentOS/RHEL
sudo yum install tesseract tesseract-langpack-chi_sim

# 或使用dnf
sudo dnf install tesseract tesseract-langpack-chi_sim
```

#### macOS系统
```bash
# 使用Homebrew
brew install tesseract tesseract-lang
```

### 2. 配置应用程序

在 `application.yml` 中配置OCR参数：

```yaml
# OCR配置
ocr:
  tesseract:
    # Tesseract数据路径（可选）
    datapath: D:/tesseract/tessdata  # Windows示例
    # datapath: /usr/share/tesseract-ocr/4.00/tessdata  # Linux示例
    
    # 识别语言
    language: chi_sim+eng  # 中文简体+英文
```

### 3. 验证安装

启动应用后，查看日志中是否有以下信息：
```
INFO  - Tesseract OCR初始化成功
INFO  - 设置Tesseract识别语言: chi_sim+eng
```

如果看到错误信息，请检查Tesseract是否正确安装。

## 使用方法

### 1. 上传图片到知识库

1. 登录系统，进入知识库管理
2. 选择或创建一个知识库
3. 上传图片文件（支持 JPG、PNG、GIF、BMP 等格式）
4. 系统会自动：
   - 使用OCR提取图片中的文本
   - 对提取的文本进行向量化处理
   - 将向量存储到知识库中

### 2. 搜索图片内容

在AI问答中，可以搜索图片中的文本内容：
- 选择包含图片的知识库
- 输入与图片文本相关的问题
- 系统会返回相关的图片文本内容

## 技术实现

### 核心组件

1. **IImageOcrService**: OCR服务接口
2. **ImageOcrServiceImpl**: Tesseract OCR实现
3. **KnowledgeRagServiceImpl**: 增强的向量化处理
4. **KnowledgeDocumentServiceImpl**: 图片文档处理

### 处理流程

```
图片上传 → OCR文本提取 → 文本分割 → 向量化 → 存储到知识库
```

### 错误处理

- OCR服务不可用时，使用图片基本信息作为文本
- 文本提取失败时，记录详细错误日志
- 支持优雅降级，不影响其他功能

## 性能优化建议

1. **图片预处理**: 上传前可对图片进行预处理（调整对比度、去噪等）
2. **批量处理**: 大量图片建议分批上传
3. **语言配置**: 根据实际需求配置识别语言，减少不必要的语言包

## 故障排除

### 常见问题

1. **OCR初始化失败**
   - 检查Tesseract是否正确安装
   - 验证环境变量PATH配置
   - 确认语言包是否存在

2. **文本识别效果差**
   - 检查图片质量和清晰度
   - 调整图片对比度和亮度
   - 确认语言配置是否正确

3. **处理速度慢**
   - 考虑调整图片大小
   - 检查服务器性能
   - 优化Tesseract配置参数

### 日志分析

关键日志信息：
```
INFO  - 检测到图片文件，使用OCR提取文本
INFO  - 从图片中提取的文本长度: XXX 字符
INFO  - 图片文本分割成 XXX 个段落
```

## 扩展功能

未来可以考虑添加：
- 图片预处理功能
- 多种OCR引擎支持
- 图片内容分类
- 手写文字识别
- 表格识别功能

## 注意事项

1. 确保有足够的磁盘空间存储临时文件
2. OCR处理可能消耗较多CPU资源
3. 大图片文件可能需要较长处理时间
4. 建议定期清理临时文件和日志
