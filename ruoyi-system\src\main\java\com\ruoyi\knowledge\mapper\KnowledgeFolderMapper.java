package com.ruoyi.knowledge.mapper;

import java.util.List;
import com.ruoyi.knowledge.domain.KnowledgeFolder;

/**
 * 知识库文件夹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface KnowledgeFolderMapper 
{
    /**
     * 查询知识库文件夹
     * 
     * @param id 知识库文件夹主键
     * @return 知识库文件夹
     */
    public KnowledgeFolder selectKnowledgeFolderById(Long id);

    /**
     * 查询知识库文件夹列表
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectKnowledgeFolderList(KnowledgeFolder knowledgeFolder);

    /**
     * 查询知识库文件夹树结构
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectKnowledgeFolderTree(Long knowledgeBaseId);

    /**
     * 根据父文件夹ID查询子文件夹
     * 
     * @param parentId 父文件夹ID
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectKnowledgeFolderByParentId(Long parentId);

    /**
     * 根据知识库ID查询根文件夹
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文件夹集合
     */
    public List<KnowledgeFolder> selectRootFoldersByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 新增知识库文件夹
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    public int insertKnowledgeFolder(KnowledgeFolder knowledgeFolder);

    /**
     * 修改知识库文件夹
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    public int updateKnowledgeFolder(KnowledgeFolder knowledgeFolder);

    /**
     * 删除知识库文件夹
     * 
     * @param id 知识库文件夹主键
     * @return 结果
     */
    public int deleteKnowledgeFolderById(Long id);

    /**
     * 批量删除知识库文件夹
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeFolderByIds(Long[] ids);

    /**
     * 检查文件夹名称是否唯一
     * 
     * @param knowledgeFolder 知识库文件夹
     * @return 结果
     */
    public int checkFolderNameUnique(KnowledgeFolder knowledgeFolder);

    /**
     * 根据文件夹ID统计子文件夹数量
     * 
     * @param parentId 父文件夹ID
     * @return 子文件夹数量
     */
    public Long countSubFoldersByParentId(Long parentId);

    /**
     * 更新文件夹的文档数量
     * 
     * @param folderId 文件夹ID
     * @param documentCount 文档数量
     * @return 结果
     */
    public int updateDocumentCount(Long folderId, Long documentCount);

    /**
     * 更新文件夹的子文件夹数量
     * 
     * @param parentId 父文件夹ID
     * @param subFolderCount 子文件夹数量
     * @return 结果
     */
    public int updateSubFolderCount(Long parentId, Long subFolderCount);
}
