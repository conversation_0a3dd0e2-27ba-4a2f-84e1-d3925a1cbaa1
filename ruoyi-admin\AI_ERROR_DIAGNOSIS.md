# AI 错误诊断指南

## 🚨 当前错误分析

根据您的截图，出现了两个错误：

1. "AI 服务暂时不可用，请稍后重试"
2. "发送消息失败"

## 🔍 快速诊断步骤

### 1. 检查 API 密钥设置

**方法 1: 检查环境变量**

```bash
echo $API-KEY
```

**方法 2: 使用诊断接口**

```bash
curl -X GET "http://localhost:8080/ai/diagnostic/config"
```

### 2. 测试网络连接

```bash
curl -X GET "http://localhost:8080/ai/diagnostic/network"
```

### 3. 测试简单 AI 调用

```bash
curl -X POST "http://localhost:8080/ai/diagnostic/simple-call"
```

### 4. 查看系统信息

```bash
curl -X GET "http://localhost:8080/ai/diagnostic/system"
```

## 🛠️ 常见问题及解决方案

### 问题 1: API 密钥未设置

**症状**: "API 密钥未设置，请检查环境变量 API-KEY"

**解决方案**:

```bash
# 设置环境变量
export API-KEY=your-actual-dashscope-api-key

# 验证设置
echo $API-KEY

# 重启应用
mvn spring-boot:run
```

### 问题 2: API 密钥无效

**症状**: "API 密钥无效，请检查 API-KEY 环境变量"

**解决方案**:

1. 登录阿里云控制台
2. 检查 DashScope 服务是否开通
3. 获取正确的 API 密钥
4. 确保 API 密钥有效且未过期

### 问题 3: 网络连接问题

**症状**: "网络连接超时，请检查网络连接"

**解决方案**:

```bash
# 测试网络连接
curl -I https://dashscope.aliyuncs.com

# 检查防火墙设置
# 确保可以访问外网HTTPS服务
```

### 问题 4: API 配额超限

**症状**: "API 调用频率超限，请稍后重试"

**解决方案**:

1. 等待一段时间后重试
2. 检查阿里云控制台的 API 使用情况
3. 升级 API 套餐或增加配额

## 🔧 详细排查步骤

### 步骤 1: 验证 API 密钥格式

正确的 API 密钥格式通常是：`sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### 步骤 2: 测试 API 连接

```bash
# 直接测试阿里云API
curl -H "Authorization: Bearer $API_KEY" \
  https://dashscope.aliyuncs.com/compatible-mode/v1/models
```

### 步骤 3: 检查应用日志

```bash
# 查看详细日志
tail -f logs/ruoyi.log | grep -i "ai\|error\|exception"
```

### 步骤 4: 验证配置文件

检查 `application.yml` 中的配置：

```yaml
ai:
  qwen:
    api-key: ${API-KEY:your-api-key-here}
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    model: qwen-plus
```

## 🎯 快速修复方案

### 方案 1: 重新设置 API 密钥

```bash
# 1. 获取正确的API密钥
# 2. 设置环境变量
export API_KEY=your-correct-api-key

# 3. 验证设置
curl -X GET "http://localhost:8080/ai/diagnostic/config"

# 4. 重启应用
```

### 方案 2: 检查网络和防火墙

```bash
# 测试网络连接
ping dashscope.aliyuncs.com
curl -I https://dashscope.aliyuncs.com

# 如果无法连接，检查：
# - 防火墙设置
# - 代理配置
# - DNS解析
```

### 方案 3: 使用诊断工具

访问诊断页面：http://localhost:8080/ai/diagnostic/config

## 📋 诊断检查清单

- [ ] API 密钥已正确设置
- [ ] 环境变量 API_KEY 存在且有效
- [ ] 网络可以访问 dashscope.aliyuncs.com
- [ ] 应用配置文件正确
- [ ] 没有防火墙阻止 HTTPS 连接
- [ ] API 配额未超限
- [ ] 应用日志没有其他错误

## 🆘 如果仍然失败

### 1. 收集诊断信息

```bash
# 运行所有诊断命令
curl -X GET "http://localhost:8080/ai/diagnostic/config"
curl -X GET "http://localhost:8080/ai/diagnostic/network"
curl -X POST "http://localhost:8080/ai/diagnostic/simple-call"
curl -X GET "http://localhost:8080/ai/diagnostic/system"
```

### 2. 查看详细日志

```bash
# 查看最近的错误日志
tail -100 logs/ruoyi.log | grep -A5 -B5 "error\|exception"
```

### 3. 手动测试 API

```bash
# 直接测试阿里云API
curl -X POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen-plus",
    "messages": [{"role": "user", "content": "你好"}],
    "max_tokens": 100
  }'
```

## 🎉 成功标志

当诊断成功时，您应该看到：

1. **配置检查**: ✅ API 密钥已设置
2. **网络测试**: ✅ 网络连接正常
3. **AI 调用**: ✅ AI 调用成功
4. **实际回复**: 收到 AI 的真实回复内容

## 📞 获取帮助

如果按照以上步骤仍无法解决，请提供：

1. 诊断接口的完整输出
2. 应用启动日志
3. 具体的错误信息
4. API 密钥的前几位字符（用于验证格式）
