# 若依系统忘记密码功能完善说明（基于参考文件改写）

## 功能概述
基于参考文件中的成功实现，重新改写了若依系统的忘记密码功能，采用更简洁高效的架构设计。

## 主要改进
1. **简化架构**：直接在控制器中处理业务逻辑，减少不必要的服务层
2. **统一接口**：使用Map参数接收前端数据，更灵活
3. **完善的短信服务**：引入SmsService接口，支持多种短信服务商
4. **更好的缓存策略**：优化Redis缓存键设计和过期时间
5. **增强的安全性**：防止频繁发送验证码，延长重置令牌有效期

## 实现的功能
1. **发送短信验证码**：用户输入用户名和手机号，系统发送验证码
2. **验证身份**：验证用户名、手机号和短信验证码的匹配性
3. **重置密码**：通过验证后，用户可以设置新密码

## 修改内容

### 1. 后端权限配置
**文件**: `ruoyi-framework/src/main/java/com/ruoyi/framework/config/SecurityConfig.java`
- 添加忘记密码相关接口到白名单：`/sendSmsCode`, `/verifyPhone`, `/resetPassword`

### 2. 前端路由配置
**文件**: `ruoyi-ui/RuoYi-Vue3/src/router/index.js`
- 添加忘记密码页面路由：`/forgot-password`

**文件**: `ruoyi-ui/RuoYi-Vue3/src/permission.js`
- 添加忘记密码页面到前端白名单

### 3. 前端API定义
**文件**: `ruoyi-ui/RuoYi-Vue3/src/api/login.js`
- 添加三个API方法：
  - `sendSmsCode()` - 发送短信验证码
  - `verifyPhone()` - 验证手机号和验证码
  - `resetPassword()` - 重置密码

### 4. 后端控制器
**新增文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysPasswordResetController.java`
- 处理忘记密码相关的HTTP请求

### 5. 请求体模型类
**新增文件**: 
- `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/model/SmsCodeBody.java`
- `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/model/VerifyPhoneBody.java`
- `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/model/ResetPasswordBody.java`

### 6. 业务服务类
**新增文件**: `ruoyi-framework/src/main/java/com/ruoyi/framework/web/service/SysPasswordResetService.java`
- 实现忘记密码的核心业务逻辑

### 7. 缓存常量
**文件**: `ruoyi-common/src/main/java/com/ruoyi/common/constant/CacheConstants.java`
- 添加短信验证码和重置令牌的Redis缓存键

## 业务流程

### 第一步：发送验证码
1. 用户输入用户名和手机号
2. 系统验证用户名和手机号是否匹配
3. 生成6位数字验证码
4. 将验证码存储到Redis（有效期5分钟）
5. 发送验证码到用户手机（当前为控制台输出，实际项目需接入短信服务）

### 第二步：验证身份
1. 用户输入验证码
2. 系统验证验证码是否正确且未过期
3. 验证成功后生成重置令牌
4. 将重置令牌存储到Redis（有效期10分钟）
5. 返回重置令牌给前端

### 第三步：重置密码
1. 用户输入新密码和确认密码
2. 系统验证重置令牌是否有效
3. 验证密码格式和一致性
4. 更新用户密码
5. 删除重置令牌

## 安全特性
1. **验证码有效期**：短信验证码5分钟内有效
2. **重置令牌有效期**：重置令牌10分钟内有效
3. **一次性使用**：验证码和重置令牌使用后立即删除
4. **身份验证**：必须验证用户名和手机号的匹配性
5. **密码加密**：使用BCrypt加密存储密码

## 注意事项
1. **短信服务**：当前短信发送为模拟实现（控制台输出），实际部署需要接入真实的短信服务商API
2. **Redis依赖**：功能依赖Redis存储验证码和令牌，确保Redis服务正常运行
3. **手机号验证**：用户注册时必须填写正确的手机号，否则无法使用忘记密码功能

## 测试建议
1. 确保用户表中有手机号数据
2. 启动Redis服务
3. 测试完整的忘记密码流程
4. 验证各种异常情况的处理

## 编译状态
✅ 项目编译成功，无语法错误

## 后续优化建议
1. 接入真实的短信服务商（如阿里云短信、腾讯云短信等）
2. 添加验证码发送频率限制
3. 添加IP限制防止恶意攻击
4. 增加操作日志记录
5. 支持邮箱验证方式
